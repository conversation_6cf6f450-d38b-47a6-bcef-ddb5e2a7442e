package spark.ability.connection.connector;

import com.trs.moye.ability.entity.operator.BatchOperatorRuntimeEntity.TableInfo;
import com.trs.moye.base.common.utils.StringUtils;
import com.trs.moye.base.common.utils.conditions.SqlUtils;
import com.trs.moye.base.data.connection.entity.params.MysqlConnectionParams;
import com.trs.moye.base.data.model.enums.DataSaveMode;
import com.trs.moye.base.data.service.entity.Condition;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SaveMode;
import org.apache.spark.sql.SparkSession;

/**
 * mysql
 *
 * <AUTHOR>
 * @since 2024/10/31 10:11
 */
@Slf4j
public class MysqlConnector implements Connector {

    @Override
    public Dataset<Row> getInputDf(SparkSession sparkSession, TableInfo storageParams, List<Condition> conditions,
        String sql) {
        if (storageParams.getConnectionParams() instanceof MysqlConnectionParams) {
            MysqlConnectionParams mysqlParams = (MysqlConnectionParams) storageParams.getConnectionParams();
            String table = storageParams.getDbTable();

            String subquery = table;
            if (Objects.nonNull(conditions) && !conditions.isEmpty()) {
                String querySql = SqlUtils.getQuerySql(conditions, mysqlParams.getConnectionType(), table);
                subquery = String.format("(%s) as subquery", querySql);
            }
            if (StringUtils.isNotEmpty(sql)) {
                subquery = String.format("(%s) as subquery", sql);
            }
            storageParams.setSubquery(subquery);
            return sparkSession.read().format("jdbc")
                .option("driver", mysqlParams.getJdbcDriverName(null))
                .option("url", mysqlParams.getJdbcUrl(null))
                .option("dbtable", subquery)
                .option("user", mysqlParams.getUsername())
                .option("password", mysqlParams.getDecryptedPassword())
                .load();
        } else {
            throw new IllegalArgumentException("mysql connect param type wrong!");
        }
    }

    @Override
    public void save(SparkSession sparkSession, Dataset<Row> df, TableInfo storageParams) {
        if (storageParams.getConnectionParams() instanceof MysqlConnectionParams) {
            MysqlConnectionParams mysqlParams = (MysqlConnectionParams) storageParams.getConnectionParams();
            df.write()
                .mode(DataSaveMode.DROP_DATA.equals(storageParams.getSaveMode()) ? SaveMode.Overwrite : SaveMode.Append)
                .format("jdbc")
                .option("driver", mysqlParams.getJdbcDriverName(null))
                .option("url", mysqlParams.getJdbcUrl(null))
                .option("dbtable", storageParams.getDbTable())
                .option("user", mysqlParams.getUsername())
                .option("password", mysqlParams.getDecryptedPassword())
                .save();
        } else {
            throw new IllegalArgumentException("mysql connect param type wrong!");
        }

    }
}
