<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.moye.base.data.model.dao.AggregationTableArrangementMapper">
    <resultMap id="BaseResultMap" type="com.trs.moye.base.data.model.entity.AggregationTableArrangement">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="canvas" property="canvas" typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler"/>
        <result column="source_tables" property="sourceTables" typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler"/>
        <result column="aggregation_table" property="aggregationTable" typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler"/>
        <result column="join_relations" property="joinRelations" typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <select id="getByModelId" resultMap="BaseResultMap">
        SELECT *
        FROM aggregation_table_arrangement
        WHERE data_model_id = #{dataModelId} limit 1
    </select>
</mapper>