package com.trs.ai.moye.data.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.ai.moye.data.model.dto.arrangement.ArrangeOperatorDTO;
import com.trs.moye.base.data.model.entity.Canvas;
import com.trs.ai.moye.data.model.dto.arrangement.StreamArrangementDTO;
import com.trs.moye.base.common.entity.AuditBaseEntity;
import com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 流处理算子编排，用于返回给前端(StreamArrangement)数据访问类
 *
 * <AUTHOR>
 * @since 2024-10-11 14:27:53
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@TableName(value = "stream_arrangement", autoResultMap = true)
public class StreamArrangement extends AuditBaseEntity  {

    /**
     * 数据建模id
     */
    private Integer dataModelId;

    /**
     * 编排信息
     */
    @TableField(typeHandler = CustomJacksonTypeHandler.class)
    private ArrangeOperatorDTO[] arrangement;

    /**
     * 画布信息
     */
    @TableField(typeHandler = CustomJacksonTypeHandler.class)
    private Canvas canvas;

    /**
     * 数据入库存储点信息
     */
    @TableField(typeHandler = CustomJacksonTypeHandler.class)
    private List<Integer> storageIds;


    public StreamArrangement(Integer dataModelId, StreamArrangementDTO request) {
        this.dataModelId = dataModelId;
        this.canvas = request.getCanvas();
        this.arrangement = request.getOperators().toArray(new ArrangeOperatorDTO[0]);
    }
}