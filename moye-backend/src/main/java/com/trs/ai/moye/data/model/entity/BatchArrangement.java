package com.trs.ai.moye.data.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.ai.moye.data.model.dto.arrangement.BatchOperatorDetailVO;
import com.trs.moye.base.data.model.entity.Canvas;
import com.trs.ai.moye.data.model.enums.ArrangeDisplayType;
import com.trs.moye.base.common.entity.AuditBaseEntity;
import com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
 
/**
 * (BatchArrangement)数据访问类
 *
 * <AUTHOR>
 * @since 2024-10-21 15:19:40
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "batch_arrangement", autoResultMap = true)
public class BatchArrangement extends AuditBaseEntity {
                                                                                                                                                                                                                                                            
    /**
     * 元数据id 
     */ 
    private Integer dataModelId;
                                                                    
    /**
     * 展示类型 画布/代码 
     */ 
    private ArrangeDisplayType displayType;
                                                                    
    /**
     * dag 
     */
    @TableField(typeHandler = CustomJacksonTypeHandler.class)
    private BatchOperatorDetailVO[] arrangement;
                                                                    
    /**
     * 代码 
     */
    @TableField(typeHandler = CustomJacksonTypeHandler.class)
    private CodeSubTasks[] codeSubTasks;

    /**
     * 画布信息 
     */
    @TableField(typeHandler = CustomJacksonTypeHandler.class)
    private Canvas canvas;
                                                                    
    /**
     * 任务是否有更新 true: 有更新 false: 没有更新(暂时针对代码模式)
     */ 
    private Boolean isUpdatedTasks;
}