package com.trs.ai.moye.data.model.dto.arrangement;

import com.trs.moye.base.data.model.entity.Canvas;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 保存要素编排请求
 *
 * <AUTHOR>
 * @since 2024/5/17 14:49
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StreamArrangementDTO {

    /**
     * 算子信息
     */
    @Valid
    @NotEmpty(message = "operators不能为空")
    private List<ArrangeOperatorDTO> operators;
    /**
     * 画布信息
     */
    @NotNull(message = "canvas不能为空")
    private Canvas canvas;

}
