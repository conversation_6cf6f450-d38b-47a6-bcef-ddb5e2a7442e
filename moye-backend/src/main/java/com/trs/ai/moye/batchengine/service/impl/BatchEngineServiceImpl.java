package com.trs.ai.moye.batchengine.service.impl;

import com.trs.ai.moye.batchengine.entity.BatchEngineTaskParam;
import com.trs.ai.moye.batchengine.entity.ExecuteResultMap;
import com.trs.ai.moye.batchengine.entity.ExecuteResultRequest;
import com.trs.ai.moye.batchengine.feign.BatchEngineFeignService;
import com.trs.ai.moye.batchengine.response.CodeFormatterResponse;
import com.trs.ai.moye.batchengine.service.BatchEngineService;
import com.trs.ai.moye.data.model.request.CodeFormatterRequest;
import com.trs.moye.base.common.exception.BizException;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 批处理引擎服务
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/10/23 15:25
 **/
@Service
@Slf4j
public class BatchEngineServiceImpl implements BatchEngineService {

    @Resource
    private BatchEngineFeignService batchEngineFeignService;


    @Override
    public Boolean executeCode(List<BatchEngineTaskParam> tasks) {
        if (tasks.isEmpty()) {
            return false;
        }
        try {
            batchEngineFeignService.execute(tasks);
        } catch (Exception e) {
            log.error("执行批处理引擎任务失败!任务参数：{}", tasks, e);
            throw new BizException("执行批处理引擎任务失败!", e);
        }
        return true;
    }

    @Override
    public Boolean executeJava(BatchEngineTaskParam task) {
        try {
            batchEngineFeignService.dagExecute(task);
        } catch (Exception e) {
            log.error("立即执行批处理dag任务失败！任务参数：{}", task, e);
            throw new BizException("立即执行批处理dag任务失败！", e);
        }
        return true;
    }

    @Override
    public String formatCode(CodeFormatterRequest request) {
        try {
            CodeFormatterResponse response = batchEngineFeignService.formatCode(request);
            if (Objects.nonNull(response)) {
                return response.getCode();
            } else {
                throw new BizException("响应结果为空，代码格式化失败！");
            }
        } catch (Exception e) {
            log.error("代码格式化失败！参数：{}", request, e);
            throw new BizException("代码格式化失败！", e);
        }
    }

    @Override
    public List<ExecuteResultMap> executeResult(ExecuteResultRequest executeResultRequest) {
        return batchEngineFeignService.executeResult(executeResultRequest);
    }

    @Override
    public void killTask(String executeId) {
        batchEngineFeignService.kill(executeId);
    }
}
