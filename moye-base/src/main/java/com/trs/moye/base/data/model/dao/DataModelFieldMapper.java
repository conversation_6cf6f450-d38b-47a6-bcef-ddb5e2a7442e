package com.trs.moye.base.data.model.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.moye.base.common.enums.FieldType;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.base.data.storage.nebula.ChangeFieldNameDto;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * DataModelField数据访问层
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/09/26 17:15
 */
@Mapper
public interface DataModelFieldMapper extends BaseMapper<DataModelField> {

    /**
     * 条件删除：通过dataModelId删除
     *
     * @param dataModelId 元数据id
     * @return int
     */
    int deleteByDataModelId(@Param("dataModelId") Integer dataModelId);

    /**
     * 主键查询
     *
     * @param id id
     * @return DataModelField
     */
    DataModelField selectByPrimaryKey(@Param("id") Integer id);

    /**
     * id集合查询
     *
     * @param idCollection id集合
     * @return 数据列表
     */
    List<DataModelField> selectByIdCollection(@Param("idCollection") Collection<Integer> idCollection);

    /**
     * 单条查询：通过zhName查询
     *
     * @param zhName 中文名
     * @return DataModelField
     */
    DataModelField getByZhName(@Param("zhName") String zhName);

    /**
     * 单条查询：通过enName查询
     *
     * @param enName 英文名
     * @return DataModelField
     */
    DataModelField getByEnName(@Param("enName") String enName);

    /**
     * 多条查询：通过dataModelId查询
     *
     * @param dataModelId 元数据id
     * @return 数据列表
     */
    List<DataModelField> listByDataModelId(@Param("dataModelId") Integer dataModelId);

    /**
     * 集合查询：通过zhNameCollection查询
     *
     * @param zhNameCollection 中文名集合
     * @return 数据列表
     */
    List<DataModelField> listByZhNameCollection(@Param("zhNameCollection") Collection<String> zhNameCollection);

    /**
     * 集合查询：通过enNameCollection查询
     *
     * @param enNameCollection 英文名集合
     * @param dataModelId      元数据id
     * @return 数据列表
     */
    List<DataModelField> listByEnNameCollection(@Param("enNameCollection") Collection<String> enNameCollection,
        @Param("dataModelId") Integer dataModelId);

    /**
     * 集合查询：通过dataModelIdCollection查询
     *
     * @param dataModelIdCollection 元数据id集合
     * @return 数据列表
     */
    List<DataModelField> listByDataModelIdCollection(
        @Param("dataModelIdCollection") Collection<Integer> dataModelIdCollection);

    /**
     * 列表查询
     *
     * @param zhName 中文名
     * @return 数据列表
     */
    List<DataModelField> listQuery(@Param("zhName") String zhName);

    /**
     * 通过英文名查询标准字段
     *
     * @param enName 英文名
     * @return 标准字段
     */
    List<DataModelField> listStandardFieldByEnName(@Param("enName") String enName);


    /**
     * 通过dataModelId查询
     *
     * @param dataModelId 数据建模ID
     * @return {@link DataModelField}
     * <AUTHOR>
     * @since 2024/10/9 15:55
     */
    List<DataModelField> selectByDataModelId(@Param("dataModelId") Integer dataModelId);


    /**
     * 查询默认审计字段id列表
     *
     * @return {@link Integer}
     * <AUTHOR>
     * @since 2024/10/9 16:15
     */
    List<Integer> selectDefaultAuditFields();

    /**
     * 通过dataModelId查询
     *
     * @param ids id列表
     * @return {@link DataModelField}
     */
    List<DataModelField> selectByIds(@Param("ids") List<Integer> ids);

    /**
     * 查询增量字段
     *
     * @param dataModelId 元数据id
     * @return 增量字段
     */
    DataModelField getIncrementField(@Param("dataModelId") Integer dataModelId);

    /**
     * 查询主键字段
     *
     * @param dataModelId 元数据id
     * @return 主键字段
     */
    DataModelField getPrimaryKey(@Param("dataModelId") Integer dataModelId);

    /**
     * 查询标准字段被数据建模使用次数
     *
     * @param standardFieldEnName 英文名
     * @return 数量
     */
    Integer countStandardFieldUsage(@Param("standardFieldEnName") String standardFieldEnName);

    /**
     * 更新系统属性类型
     *
     * @param type        属性类型
     * @param oldTypeName 旧属性
     * @param newTypeName 新属性
     * <AUTHOR>
     * @since 2021/6/1 22:43
     */
    void updateEntityTypeName(@Param("type") String type, @Param("oldTypeName") String oldTypeName,
        @Param("newTypeName") String newTypeName);

    /**
     * 根据类型查询
     *
     * @param type     字段类型
     * @param typeName 类型名称
     * @return 字段
     */
    List<DataModelField> selectByTypeTypeName(@Param("type") String type, @Param("typeName") String typeName);

    /**
     * 更新数据
     *
     * @param field 字段
     */
    void updateDefaultValue(@Param("field") DataModelField field);

    /**
     * 根据英文名和类型删除和数据建模id删除
     *
     * @param type2EnNames 类型和英文名
     * @param dataModelIds 数据建模id
     */
    void deleteByEnNameAndTypeAndDataModelIds(@Param("type2EnNames") Map<FieldType, List<String>> type2EnNames,
        @Param("dataModelIds") List<Integer> dataModelIds);

    /**
     * 根据数据建模id和字段列表修改字段名称
     *
     * @param dataModelId 数据建模id
     * @param fields      字段列表
     */
    void updateFieldsName(@Param("dataModelId") Integer dataModelId, @Param("fields") List<ChangeFieldNameDto> fields);

    /**
     * 根据存储id建模字段
     *
     * @param storageIds 存储id列表
     * @return 字段列表
     */
    List<DataModelField> selectByStorageIds(@Param("storageIds") Collection<Integer> storageIds);

    /**
     * 查询已经建表的字段
     *
     * @param dataModelId 数据建模id
     * @return 建表字段列表
     */
    List<DataModelField> selectCreateTableFields(@Param("dataModelId") Integer dataModelId);

    /**
     * 根据数据建模id查询标题字段名称
     *
     * @param dataModelId 数据建模id
     * @return 字段名称
     */
    String selectTitleFieldNameByDataModelId(@Param("dataModelId") Integer dataModelId);
}