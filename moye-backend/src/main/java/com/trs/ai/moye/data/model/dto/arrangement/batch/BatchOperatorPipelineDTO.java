package com.trs.ai.moye.data.model.dto.arrangement.batch;

import com.trs.moye.base.data.model.entity.Canvas;
import com.trs.moye.base.common.annotaion.validation.NotRepeat;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 批处理编排dto
 *
 * <AUTHOR>
 * @since 2025/3/21 15:45
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BatchOperatorPipelineDTO {

    /**
     * 画布信息
     */
    private Canvas canvas;

    /**
     * 批处理编排
     */
    @Valid
    @NotEmpty(message = "算子列表不能为空!")
    @NotRepeat(fields = {"displayId:算子id"})
    private List<BatchOperatorDTO> operators;

}
