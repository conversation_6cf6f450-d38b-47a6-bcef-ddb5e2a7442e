package com.trs.ai.moye.data.model.service.impl;

import com.trs.ai.moye.data.model.dto.ExecuteParamDTO;
import com.trs.ai.moye.data.model.request.CreateTableRequests.CreateTableRequest;
import com.trs.ai.moye.data.model.request.DwdStartRequest;
import com.trs.ai.moye.data.model.request.IndicatorFieldRequest;
import com.trs.ai.moye.data.model.request.indicator.IndicatorAddRequest;
import com.trs.ai.moye.data.model.request.indicator.IndicatorConfigUpdateRequest;
import com.trs.ai.moye.data.model.request.indicator.IndicatorDetailDataSearchRequest;
import com.trs.ai.moye.data.model.request.indicator.IndicatorSearchRequest;
import com.trs.ai.moye.data.model.request.indicator.IndicatorStartRequest;
import com.trs.ai.moye.data.model.request.indicator.IndicatorTimeRangeParams;
import com.trs.ai.moye.data.model.response.CreateTableResponse;
import com.trs.ai.moye.data.model.response.DwdAddResponse;
import com.trs.ai.moye.data.model.response.StorageEngineResponse;
import com.trs.ai.moye.data.model.response.indicator.IndicatorConfigResponse;
import com.trs.ai.moye.data.model.response.indicator.IndicatorDataPreviewResponse;
import com.trs.ai.moye.data.model.service.BatchArrangementService;
import com.trs.ai.moye.data.model.service.DataModelScheduleService;
import com.trs.ai.moye.data.model.service.DataModelService;
import com.trs.ai.moye.data.model.service.DwdModelService;
import com.trs.ai.moye.data.model.service.IndicatorModelService;
import com.trs.ai.moye.data.model.task.start.TaskStart;
import com.trs.ai.moye.data.service.entity.DataServiceField;
import com.trs.ai.moye.data.service.entity.query.Condition;
import com.trs.ai.moye.data.service.entity.query.ValueObject;
import com.trs.ai.moye.data.service.enums.DataServiceConditionType;
import com.trs.ai.moye.data.service.enums.LogicLinkOperator;
import com.trs.ai.moye.llm.TranslateService;
import com.trs.ai.moye.out.feign.BiEngineFeign;
import com.trs.ai.moye.out.response.OutIndicatorAvailablePeriodsResponse;
import com.trs.ai.moye.out.util.indicator.TimeRangeCalculator;
import com.trs.ai.moye.storageengine.entity.SortField;
import com.trs.ai.moye.storageengine.feign.SearchFeign;
import com.trs.ai.moye.storageengine.request.CodeSearchParams;
import com.trs.ai.moye.storageengine.response.StorageSearchResponse;
import com.trs.ai.moye.storageengine.service.StorageEngineService;
import com.trs.moye.base.common.enums.FieldType;
import com.trs.moye.base.common.enums.TimeRange;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.help.AddUpdateDeleteSeparator;
import com.trs.moye.base.common.request.SearchParams;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.moye.base.common.utils.DateTimeUtils;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.data.indicator.dao.IndicatorConfigMapper;
import com.trs.moye.base.data.indicator.dao.IndicatorFieldMapper;
import com.trs.moye.base.data.indicator.dao.IndicatorPeriodConfigMapper;
import com.trs.moye.base.data.indicator.entity.DataModelIndicatorField;
import com.trs.moye.base.data.indicator.entity.DataModelIndicatorFieldEntity;
import com.trs.moye.base.data.indicator.entity.DataModelIndicatorFieldResponse;
import com.trs.moye.base.data.indicator.entity.IndicatorConfig;
import com.trs.moye.base.data.indicator.entity.IndicatorDataSearchParams;
import com.trs.moye.base.data.indicator.entity.IndicatorPeriodConfigEntity;
import com.trs.moye.base.data.indicator.entity.IndicatorSortField;
import com.trs.moye.base.data.indicator.entity.IndicatorStatisticStrategyInfo;
import com.trs.moye.base.data.indicator.entity.StatisticPeriod;
import com.trs.moye.base.data.indicator.enums.IndicatorConstants;
import com.trs.moye.base.data.indicator.enums.IndicatorPeriodType;
import com.trs.moye.base.data.indicator.enums.IndicatorSearchRange;
import com.trs.moye.base.data.indicator.enums.IndicatorSortOrder;
import com.trs.moye.base.data.indicator.enums.IndicatorTimeRange;
import com.trs.moye.base.data.indicator.utils.IndicatorFieldGenerator;
import com.trs.moye.base.data.indicator.utils.IndicatorPeriodConverter;
import com.trs.moye.base.data.indicator.utils.IndicatorSqlConvertor;
import com.trs.moye.base.data.model.dao.DataModelExecuteConfigMapper;
import com.trs.moye.base.data.model.dao.DataModelFieldMapper;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.entity.BatchProcessSparkConfig;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.entity.DataModelExecuteConfig;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.base.data.model.entity.DataSourceConfig;
import com.trs.moye.base.data.model.enums.CreateTableStatus;
import com.trs.moye.base.data.service.enums.EvaluateOperator;
import com.trs.moye.base.data.source.dao.DataSourceConfigMapper;
import com.trs.moye.base.data.storage.DataStorage;
import com.trs.moye.base.data.storage.dao.DataStorageMapper;
import com.trs.moye.base.data.storage.setting.DefaultDataStorageSettings;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 指标模型服务实现
 *
 * <AUTHOR>
 * @since 2025/05/19 16:45:07
 */
@Service
@Slf4j
public class IndicatorModelServiceImpl implements IndicatorModelService {

    @Resource
    private DataModelMapper dataModelMapper;

    @Resource
    private DataModelScheduleService dataModelScheduleService;

    @Resource
    private DwdModelService dwdModelService;

    @Resource
    private IndicatorPeriodConfigMapper indicatorPeriodConfigMapper;

    @Resource
    private IndicatorConfigMapper indicatorConfigMapper;

    @Resource
    private DataModelExecuteConfigMapper dataModelExecuteConfigMapper;

    @Resource
    private TaskStart taskStart;

    @Resource
    private DataStorageMapper dataStorageMapper;

    @Resource
    private DataSourceConfigMapper dataSourceConfigMapper;

    @Resource
    private StorageEngineService storageEngineService;

    @Resource
    private BatchArrangementService batchArrangementService;

    @Resource
    private SearchFeign searchFeign;

    @Resource
    private BiEngineFeign biEngineFeign;

    @Resource
    private DataModelFieldMapper dataModelFieldMapper;

    @Resource
    private TranslateService translateService;

    @Resource
    private IndicatorFieldMapper indicatorFieldMapper;

    @Resource
    private DataModelService dataModelService;

    @Resource
    private DataModelFieldService dataModelFieldService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DwdAddResponse addIndicatorModel(IndicatorAddRequest request) {

        if (Boolean.TRUE.equals(dataModelMapper.existsByEnName(request.getBasicInfo().getEnName()))) {
            throw new BizException(String.format("数据建模英文名称[%s]已存在", request.getBasicInfo().getEnName()));
        }
        //新增建模，创建dataModel
        DataModel dataModel = request.toModel();
        dataModelMapper.insert(dataModel);
        Integer dataModelId = dataModel.getId();

        IndicatorStatisticStrategyInfo statisticStrategyInfo = request.getStatisticStrategyInfo();

        IndicatorPeriodConfigEntity periodConfig = indicatorPeriodConfigMapper.selectByPeriodType(
            statisticStrategyInfo.getPeriod().name());

        //创建执行配置
        DataModelExecuteConfig executeConfig = new DataModelExecuteConfig();
        executeConfig.setDataModelId(dataModelId);
        dataModelExecuteConfigMapper.insert(executeConfig);

        //创建调度配置
        dataModelScheduleService.createScheduleConfig(dataModel,
            IndicatorPeriodConverter.convert(periodConfig.getConfig()));

        //保存数据来源
        dwdModelService.createDataSources(dataModelId,
            Collections.singletonList(request.getDataSourceId()));

        //保存指标详细配置
        IndicatorConfig indicatorConfig = request.toIndicatorConfig(dataModelId);
        indicatorConfigMapper.insert(indicatorConfig);

        //存储点在默认ck指标库
        DataStorage dataStorage = new DataStorage();
        dataStorage.setDataModelId(dataModelId);
        dataStorage.setConnectionId(IndicatorConstants.INDICATOR_CONNECTION_ID);
        dataStorage.setEnName(dataModel.getEnName());
        dataStorage.setZhName(dataModel.getZhName());
        dataStorage.setCreateTableStatus(CreateTableStatus.NOT);
        dataStorageMapper.insert(dataStorage);

        //保存字段
        request.getFields().forEach(field -> {
            field.setDataModelId(dataModelId);
            //审计字段默认不为空；以防止ck建表排序字段不可为空
            if (field.getEnName().equals("insert_time")) {
                field.setNullable(false);
            }
            dataModelFieldMapper.insert(field);
            DataModelIndicatorFieldEntity indicatorFieldEntity = new DataModelIndicatorFieldEntity(field);
            indicatorFieldMapper.insert(indicatorFieldEntity);
        });

        return new DwdAddResponse(dataModelId, dataModel.getLayer());
    }

    @Override
    public IndicatorConfigResponse getIndicatorConfig(Integer id) {
        DataModel dataModel = dataModelMapper.selectById(id);
        List<DataSourceConfig> dataSource = dataModel.getDataSource();
        DataModel dataSource2 = dataModelMapper.selectByIds(
            dataSource.stream().map(DataSourceConfig::getSourceModelId).toList()).stream().findFirst().orElse(null);
        IndicatorConfig indicatorConfig = indicatorConfigMapper.selectByDataModelId(id);

        return new IndicatorConfigResponse(dataSource2, indicatorConfig);
    }

    @Override
    public List<DataModelIndicatorFieldResponse> getIndicatorFields(Integer id) {
        // 获取所有需要的数据
        List<DataModelField> dataModelFields = dataModelFieldMapper.selectByDataModelId(id);
        List<DataModelIndicatorFieldEntity> dataModelIndicatorFieldEntities = indicatorFieldMapper.listByDataModelId(
            id);
        Set<Integer> modelCreateTableFieldIds = dataModelService.getModelCreateTableFieldIds(id);

        // 将 dataModelFields 转换为 Map
        Map<Integer, DataModelField> fieldMap = dataModelFields.stream()
            .collect(Collectors.toMap(DataModelField::getId, field -> field));

        return dataModelIndicatorFieldEntities.stream().map(
                field -> Optional.ofNullable(fieldMap.get(field.getDataModelFieldId())).map(
                    dataModelField -> new DataModelIndicatorFieldResponse(dataModelField, field.getIndicatorType(),
                        field.isEnable(), field.getOriginalZhName(), field.getConfig(),
                        modelCreateTableFieldIds.contains(field.getDataModelFieldId()), field.getExecuteOrder())))
            .filter(Optional::isPresent).map(Optional::get).toList();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveFieldList(Integer id, List<IndicatorFieldRequest> requestList) {
        List<DataModelIndicatorFieldEntity> dataModelIndicatorFieldEntities = indicatorFieldMapper.listByDataModelId(
            id);
        dataModelFieldService.saveFieldList(id,
            requestList.stream().map(IndicatorFieldRequest::toModelFieldRequest).toList());
        List<DataModelField> modelFields = dataModelFieldMapper.listByDataModelId(id);
        AddUpdateDeleteSeparator<IndicatorFieldRequest, Integer> separator = AddUpdateDeleteSeparator.create(
            dataModelIndicatorFieldEntities, DataModelIndicatorFieldEntity::getDataModelFieldId, requestList,
            IndicatorFieldRequest::getId);
        // 修改字段
        if (ObjectUtils.isNotEmpty(separator.getUpdateDataList())) {
            for (IndicatorFieldRequest request : separator.getUpdateDataList()) {
                DataModelIndicatorFieldEntity field = request.toDataModelIndicatorFieldEntity(id);
                indicatorFieldMapper.updateByDataModelId(field);
            }
        }
        // 添加字段
        if (ObjectUtils.isNotEmpty(separator.getAddDataList())) {
            for (IndicatorFieldRequest request : separator.getAddDataList()) {
                DataModelIndicatorFieldEntity field = request.toDataModelIndicatorFieldEntity(id);
                field.setDataModelFieldId(
                    modelFields.stream().filter(modelField -> modelField.getEnName().equals(request.getEnName()))
                        .findFirst().orElseThrow(() -> new BizException("字段不存在：" + request.getEnName())).getId());
                indicatorFieldMapper.insert(field);
            }
        }
        // 删除字段
        List<Integer> deleteCustomFieldIds = separator.getDeleteKeyList();
        if (ObjectUtils.isNotEmpty(deleteCustomFieldIds)) {
            indicatorFieldMapper.deleteByDataModelFieldIds(deleteCustomFieldIds);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateIndicatorConfig(Integer id, IndicatorConfigUpdateRequest request) {
        // 校验数据模型是否存在
        DataModel dataModel = dataModelMapper.selectById(id);
        if (dataModel == null) {
            throw new BizException(String.format("数据建模[id:%d]不存在", id));
        }
        // 更新数据来源
        dataSourceConfigMapper.deleteByDataModelId(id);
        dwdModelService.createDataSources(id,
            Collections.singletonList(request.getDataSourceId()));
        // 更新调度配置
        IndicatorStatisticStrategyInfo statisticStrategyInfo = request.getStatisticStrategyInfo();
        IndicatorPeriodConfigEntity periodConfig = indicatorPeriodConfigMapper.selectByPeriodType(
            statisticStrategyInfo.getPeriod().name());
        dataModelScheduleService.updateModelScheduleConfig(id,
            IndicatorPeriodConverter.convert(periodConfig.getConfig()));
        // 构建并更新指标配置
        IndicatorConfig config = request.toIndicatorConfig(id);
        indicatorConfigMapper.updateById(config);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void startIndicatorTask(Integer id, DwdStartRequest request) {
        //启动的时候建表
        CreateTableResponse createTableResponse = createTable(id);
        if (!createTableResponse.isSuccess()) {
            throw new BizException("建表失败！原因：" + createTableResponse.getMessage());
        }
        ExecuteParamDTO executeParamDTO = new ExecuteParamDTO(request);
        // 启动时默认触发立即执行
        batchArrangementService.execute(id,
            new BatchProcessSparkConfig(request.getCertificateId(), request.getSparkConfigItemList(),
                request.getCustomCodeParameters()));
        taskStart.startTask(id, executeParamDTO);
    }

    @Override
    public void rerunIndicatorTask(Integer id, IndicatorStartRequest request) {
        //立即执行的时候建表
        CreateTableResponse createTableResponse = createTable(id);
        if (!createTableResponse.isSuccess()) {
            throw new BizException("建表失败！原因：" + createTableResponse.getMessage());
        }
        // 立即执行
        batchArrangementService.execute(id,
            new BatchProcessSparkConfig(request.getCertificateId(), request.getSparkConfigItemList(),
                request.getCustomCodeParameters(), request.getBeginTime(), request.getEndTime()));
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void stopIndicatorTask(Integer id) {
        taskStart.stopTask(id);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void pauseIndicatorTask(Integer id) {
        taskStart.pauseOdsTask(id);
    }

    @Override
    public CreateTableResponse createTable(Integer id) {
        DataModel dataModel = dataModelMapper.selectById(id);
        if (dataModel.getDataStorages().size() == 0) {
            throw new BizException("指标库%d未创建存储点！", id);
        }
        DataStorage storage = dataModel.getDataStorages().get(0);
        // 检查指标库是否已创建表
        if (storage.getCreateTableStatus().equals(CreateTableStatus.SUCCESS)) {
            return new CreateTableResponse(storage.getId(), new StorageEngineResponse(true, "指标库已创建表"));
        }
        //创建指标库默认ck建表settings
        CreateTableRequest tableRequest = new CreateTableRequest();
        tableRequest.setConnectionId(storage.getConnectionId());
        tableRequest.setDataStorageId(storage.getId());
        DefaultDataStorageSettings settings = new DefaultDataStorageSettings();
        settings.setConnectionType(ConnectionType.CLICK_HOUSE);
        settings.setEngine("MergeTree");
        //TODO 确定默认排序字段
        settings.setOrderField(IndicatorConstants.START_TIME);
        // 分区字段, 统计开始时间加8
        settings.setPartitionBy(String.format("toDate(addHours(`%s`, 8))", IndicatorConstants.START_TIME));
        tableRequest.setSettings(settings);
        return DataModelServiceImpl.createTable(dataModel, tableRequest, dataStorageMapper, storageEngineService);
    }

    @Override
    public IndicatorDataPreviewResponse dataPreview(IndicatorAddRequest request) {
        if (Objects.isNull(request.getDataSourceId())) {
            throw new BizException("数据来源为空！");
        }
        Integer sourceId = request.getDataSourceId();
        List<DataStorage> dataStorages = dataStorageMapper.selectByDataModelIdWithConnection(sourceId);
        if (dataStorages.isEmpty()) {
            throw new BizException("数据来源的存储点为空！");
        }
        IndicatorPeriodConfigEntity periodConfig = indicatorPeriodConfigMapper.selectByPeriodType(
            request.getStatisticStrategyInfo().getPeriod().name());
        StatisticPeriod period = IndicatorPeriodConverter.calculateStatPeriod(LocalDateTime.now(),
            periodConfig.getConfig());
        String sql = IndicatorSqlConvertor.createExecuteSql(request.getStatisticSql(), request.getConditions(),
            dataStorages.get(0).getConnection().getConnectionType(), period);
        log.info("指标库 - 解析sql结果：{}", sql);
        CodeSearchParams codeSearchParams = CodeSearchParams.fromSql(sql);
        if (Objects.nonNull(request.getPageParams())) {
            codeSearchParams.setPageParams(request.getPageParams());
        }
        IndicatorDataPreviewResponse response = new IndicatorDataPreviewResponse();
        try {
            IndicatorDataPreviewResponse searchResult = searchFeign.indicatorPreview(
                dataStorages.get(0).getConnectionId(), codeSearchParams);
            response.setData(searchResult.getData());
            response.setErrorMsg(searchResult.getErrorMsg());
        } catch (Exception e) {
            response.setErrorMsg("sql执行报错：" + e.getMessage());
        }
        response.setExecuteSql(sql);
        List<DataModelIndicatorField> fields = new ArrayList<>();
        fields.addAll(translateService.translateFieldsBySql(sql, sourceId));
        fields.addAll(IndicatorFieldGenerator.generateDefaultFields(periodConfig.getPeriodType(), fields));
        response.setFields(fields);
        return response;
    }

    @Override
    public PageResponse<Map<String, Object>> queryIndicator(Integer dataModelId, IndicatorSearchRange searchRange,
        IndicatorDetailDataSearchRequest request) {
        List<DataStorage> dataStorages = dataStorageMapper.selectByDataModelIdWithConnection(dataModelId);
        if (dataStorages.isEmpty()) {
            throw new BizException("数据来源的存储点为空！");
        }
        IndicatorConfig indicatorConfig = indicatorConfigMapper.selectByDataModelId(dataModelId);
        StatisticPeriod period = null;
        if (indicatorConfig.getStatisticStrategyInfo() != null) {
            IndicatorPeriodConfigEntity periodConfig;
            periodConfig = indicatorPeriodConfigMapper.selectByPeriodType(
                indicatorConfig.getStatisticStrategyInfo().getPeriod().name());
            period = IndicatorPeriodConverter.calculateStatPeriod(LocalDateTime.now(), periodConfig.getConfig());
            if (searchRange == IndicatorSearchRange.LAST) {
                period = IndicatorPeriodConverter.calculateStatPeriod(period.getStartTime(), periodConfig.getConfig());
            } else if (Objects.nonNull(request.getTimeRangeParams())
                && (Objects.nonNull(request.getTimeRangeParams().getYear())
                || Objects.nonNull(request.getTimeRangeParams().getDate()))) {
                period = TimeRangeCalculator.calculatePeriod(request.getTimeRangeParams(), periodConfig.getConfig());
            }
        }

        IndicatorSortField indicatorSortField = buildIndicatorSortField(request);
        IndicatorDataSearchParams indicatorSearchParams = IndicatorDataSearchParams.builder()
            .dataModelId(dataModelId)
            .tableName(dataStorages.get(0).getEnName())
            .sortField(indicatorSortField)
            .statisticPeriod(period)
            .conditions(request.getConditions())
            .build();

        return biEngineFeign.indicatorQuery(dataStorages.get(0).getConnectionId(),
            indicatorSearchParams);
    }

    @NotNull
    private static IndicatorSortField buildIndicatorSortField(IndicatorDetailDataSearchRequest request) {
        IndicatorSortField indicatorSortField = new IndicatorSortField();
        if (CollectionUtils.isNotEmpty(request.getSortFields())) {
            SortField sortField = request.getSortFields().stream().findFirst().orElse(new SortField());
            if (Objects.nonNull(sortField.getField()) && Objects.nonNull(sortField.getOrder())) {
                indicatorSortField.setField(sortField.getField());
                indicatorSortField.setOrder(IndicatorSortOrder.valueOf(sortField.getOrder().name()));
            }
        }
        return indicatorSortField;
    }

    @Override
    public PageResponse<Map<String, Object>> getDataPreviewPageList(Integer id, IndicatorSearchRequest request) {
        IndicatorTimeRangeParams timeRangeParams = request.getTimeRangeParams();

        List<Condition> conditions = request.getConditions();
        //时间参数前需要拼接and
        if (!conditions.isEmpty()) {
            conditions.add(new Condition(LogicLinkOperator.AND));
        }
        conditions.addAll(createConditions(id, timeRangeParams));
        //检索参数
        conditions.addAll(createSearchKeywordCondition(request.getSearchParams()));

        request.setConditions(conditions);
        DataStorage storage = dataStorageMapper.selectByDataModelIdWithConnection(id).get(0);
        StorageSearchResponse storageSearchResponse = storageEngineService.conditionQuery(storage.getConnectionId(),
            storage.getEnName(), request);
        return PageResponse.of(storageSearchResponse.getItems(), request.getPageParams().getPageNum(),
            storageSearchResponse.getTotal(), request.getPageParams().getPageSize());
    }

    @Override
    public OutIndicatorAvailablePeriodsResponse getAvailablePeriods(Integer id, Integer year) {
        IndicatorConfig indicatorConfig = indicatorConfigMapper.selectByDataModelId(id);
        if (indicatorConfig == null || indicatorConfig.getStatisticStrategyInfo() == null) {
            throw new BizException("指标配置不存在或统计策略信息不完整");
        }
        IndicatorPeriodConfigEntity periodConfig = indicatorPeriodConfigMapper.selectByPeriodType(
            indicatorConfig.getStatisticStrategyInfo().getPeriod().name());
        if (periodConfig == null) {
            throw new BizException("指标周期配置不存在");
        }
        IndicatorPeriodConfigEntity yearPeriod = indicatorPeriodConfigMapper.selectByPeriodType(
            IndicatorPeriodType.YEARLY.name());

        return TimeRangeCalculator.calculateAvailablePeriods(year, periodConfig.getPeriodType(),
            periodConfig.getConfig(), yearPeriod.getConfig());
    }

    private List<Condition> createConditions(Integer dataModelId, IndicatorTimeRangeParams timeRangeParams) {
        LocalDateTime startTime;
        LocalDateTime endTime;
        if (timeRangeParams.getType().equals(IndicatorTimeRange.ALL)) {
            startTime = TimeRange.ALL.getBeginTime();
            endTime = TimeRange.ALL.getEndTime();
        } else if (timeRangeParams.getType().equals(IndicatorTimeRange.CUSTOM)) {
            startTime = timeRangeParams.getBeginTime();
            endTime = timeRangeParams.getEndTime();
        } else {
            IndicatorConfig indicatorConfig = indicatorConfigMapper.selectByDataModelId(dataModelId);
            IndicatorPeriodConfigEntity periodConfig = indicatorPeriodConfigMapper.selectByPeriodType(
                indicatorConfig.getStatisticStrategyInfo().getPeriod().name());
            StatisticPeriod period = timeRangeParams.getType().getPeriodTime(periodConfig.getConfig());
            startTime = period.getStartTime();
            endTime = period.getEndTime();
        }
        List<Condition> conditions = new ArrayList<>();
        conditions.add(createTimeCondition(startTime, IndicatorConstants.START_TIME, EvaluateOperator.GTE));
        conditions.add(new Condition(LogicLinkOperator.AND));
        conditions.add(createTimeCondition(endTime, IndicatorConstants.END_TIME, EvaluateOperator.LTE));
        return conditions;
    }

    private Condition createTimeCondition(LocalDateTime time, String timeField, EvaluateOperator operator) {
        Condition condition = new Condition();
        DataServiceField field = new DataServiceField();
        field.setEnName(timeField);
        field.setType(FieldType.DATETIME);
        condition.setKey(field);
        condition.setOperator(operator.getValue());
        condition.setType(DataServiceConditionType.EXPRESSION);
        condition.setValues(List.of(new ValueObject(DateTimeUtils.formatStr(time))));
        return condition;
    }

    private List<Condition> createSearchKeywordCondition(SearchParams searchParams) {
        List<Condition> conditions = new ArrayList<>();
        String keyword = searchParams.getKeyword() == null ? "" : searchParams.getKeyword().toString();

        if (StringUtils.isNotBlank(keyword)) {
            conditions.add(new Condition(LogicLinkOperator.AND));
            conditions.add(new Condition(LogicLinkOperator.LEFT_PARENTHESES));
            for (int i = 0; i < searchParams.getFields().size(); i++) {
                if (i > 0) {
                    conditions.add(new Condition(LogicLinkOperator.OR));
                }
                Condition condition = new Condition();
                DataServiceField field = new DataServiceField();
                field.setEnName(searchParams.getFields().get(i));
                condition.setKey(field);
                condition.setOperator(EvaluateOperator.CONTAIN.getValue());
                condition.setType(DataServiceConditionType.EXPRESSION);
                condition.setValues(List.of(new ValueObject(searchParams.getKeyword().toString())));
                conditions.add(condition);
            }
            conditions.add(new Condition(LogicLinkOperator.RIGHT_PARENTHESES));
        }

        return conditions;
    }
}
