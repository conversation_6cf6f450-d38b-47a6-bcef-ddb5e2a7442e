package com.trs.moye.base.data.model.request.arrangement.theme;

import com.trs.moye.base.data.model.entity.Canvas;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025-07-31 14:58
 */
@Data
public class ArrangeAggregationTable {

    /**
     * 画布信息
     */
    private Canvas canvas;

    /**
     * 字段列表
     */
    @NotEmpty(message = "聚合表字段列表不能为空")
    private List<AggregationField> fields;

}
