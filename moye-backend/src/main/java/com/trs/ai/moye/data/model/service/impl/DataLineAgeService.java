package com.trs.ai.moye.data.model.service.impl;

import com.trs.ai.moye.data.model.dao.BatchArrangementMapper;
import com.trs.ai.moye.data.model.dao.batch.BatchOperatorMapper;
import com.trs.ai.moye.data.model.dao.operator.OperatorPipelineMapper;
import com.trs.moye.base.data.model.entity.Canvas;
import com.trs.ai.moye.data.model.entity.BatchArrangement;
import com.trs.ai.moye.data.model.enums.ArrangeDisplayType;
import com.trs.ai.moye.data.model.request.DataLineageRequest;
import com.trs.ai.moye.data.model.response.DataLineageCountVO;
import com.trs.ai.moye.data.model.response.DataLineageVO;
import com.trs.ai.moye.data.model.response.DataLineageVO.DataLineageDetailVO;
import com.trs.ai.moye.homepage.dao.HomePageDwdStatisticsMapper;
import com.trs.ai.moye.homepage.dao.HomePageOdsStorageStatisticsMapper;
import com.trs.ai.moye.homepage.dao.HomePageSubjectStatisticsMapper;
import com.trs.ai.moye.homepage.dao.HomePageThemeStatisticsMapper;
import com.trs.ai.moye.homepage.enums.StatisticsDwdType;
import com.trs.ai.moye.monitor.entity.ProcessTimeRange;
import com.trs.ai.moye.monitor.utils.StatisticalTimeRangeUtils;
import com.trs.moye.ability.entity.operator.Operator;
import com.trs.moye.ability.entity.operator.OperatorPipeline;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.common.enums.TimeRange;
import com.trs.moye.base.common.request.TimeRangeParams;
import com.trs.moye.base.data.execute.ExecuteModeEnum;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.entity.DataSourceConfig;
import com.trs.moye.base.data.source.dao.DataSourceConfigMapper;
import com.trs.moye.base.data.storage.DataStorage;
import com.trs.moye.base.data.storage.dao.DataStorageMapper;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;

/**
 * 数据血源service
 *
 * <AUTHOR>
 * @since 2025/6/10
 */
@Service
@Slf4j
public class DataLineAgeService {

    @Resource
    private DataModelMapper dataModelMapper;

    @Resource
    private DataSourceConfigMapper dataSourceConfigMapper;

    @Resource
    private HomePageOdsStorageStatisticsMapper odsStorageStatisticsMapper;

    @Resource
    private HomePageDwdStatisticsMapper dwdStatisticsMapper;

    @Resource
    private BatchOperatorMapper batchOperatorMapper;

    @Resource
    private OperatorPipelineMapper operatorPipelineMapper;

    @Resource
    private DataStorageMapper dataStorageMapper;

    @Resource
    private BatchArrangementMapper batchArrangementMapper;

    @Resource
    private HomePageThemeStatisticsMapper themeStatisticsMapper;

    @Resource
    private HomePageSubjectStatisticsMapper subjectStatisticsMapper;


    /**
     * 获取数据血源
     *
     * @param id      建模id
     * @param request 时间参数
     * @return 血源
     */
    public DataLineageVO getDataLineage(Integer id, DataLineageRequest request) {
        TimeRangeParams timeRangeParams = request.getTimeRangeParams();
        DataLineageVO lineageVO = new DataLineageVO();
        lineageVO.setCanvas(Canvas.getDefaultCanvas());
        List<DataLineageDetailVO> list = new ArrayList<>();
        DataModel dataModel = dataModelMapper.selectByIdsWithStorage(List.of(id)).get(0);

        DataLineageDetailVO currentVO = new DataLineageDetailVO(dataModel);
        if (Objects.isNull(timeRangeParams) || Objects.isNull(timeRangeParams.getType())) {
            timeRangeParams = new TimeRangeParams(TimeRange.LAST_HOUR);
        }
        buildMonitorCount(currentVO, dataModel, timeRangeParams, null);
        list.add(currentVO);
        addParentTables(list, id, timeRangeParams);
        lineageVO.setTables(list);
        return lineageVO;
    }


    private void addParentTables(List<DataLineageDetailVO> list, Integer id, TimeRangeParams timeRangeParams) {
        List<DataSourceConfig> dataSource = dataSourceConfigMapper.selectByDataModel(id);
        // 构建 sourceId -> DataModel 的映射
        Map<Integer, DataModel> dataModelMap = buildSource2ModelMap(id, dataSource);
        for (DataSourceConfig r : dataSource) {
            Integer sourceId = r.getSourceModelId();
            //sourceId为null，则是贴源表，中止循环；sourceId=当前id，返回防止出现死循环
            if (sourceId == null || sourceId.equals(id)) {
                return;
            }
            Optional<DataLineageDetailVO> presentVO = list.stream().filter(vo -> vo.getId().equals(sourceId))
                .findFirst();
            if (presentVO.isPresent()) {
                presentVO.get().getTargetIds().add(id);
            } else {
                DataModel sourceModel = dataModelMap.get(sourceId);
                if (Objects.nonNull(sourceModel)) {
                    DataLineageDetailVO detailVO = new DataLineageDetailVO(sourceModel);
                    detailVO.getTargetIds().add(id);
                    buildMonitorCount(detailVO, sourceModel, timeRangeParams, id);
                    list.add(detailVO);
                }
            }
            addParentTables(list, sourceId, timeRangeParams);
        }
    }


    private void buildMonitorCount(DataLineageDetailVO detailVO, DataModel dataModel, TimeRangeParams timeRangeParams,
        Integer sourceId) {
        log.info("dataModelId: {}, dataModel: {}", dataModel.getId(), dataModel);
        log.info("sourceId: {}", sourceId);
        ProcessTimeRange timeRange = StatisticalTimeRangeUtils.getTimeRange(timeRangeParams);
        Integer storageId = null;
        storageId = getStorageId(dataModel, sourceId, storageId);
        switch (detailVO.getType()) {
            case ODS:
                log.info("ods拿到的存储点id: {}", storageId);
                log.info("ods的时间范围: {}", timeRange);
                handleOds(detailVO, dataModel, timeRange, storageId);
                break;
            case DWD:
                log.info("dwd拿到的存储点id: {}", storageId);
                log.info("dwd的时间范围: {}", timeRange);
                handleDwd(detailVO, dataModel, timeRange, storageId);
                break;
            case THEME:
                log.info("theme拿到的存储点id: {}", storageId);
                log.info("theme的时间范围: {}", timeRange);
                handleTheme(detailVO, dataModel, timeRange, storageId);
                break;
            case SUBJECT:
                log.info("subject拿到的存储点id: {}", storageId);
                log.info("subject的时间范围: {}", timeRange);
                handleSubject(detailVO, dataModel, timeRange, storageId);
                break;
            default:
                break;
        }
    }

    private @Nullable Integer getStorageId(DataModel dataModel, Integer sourceId, Integer storageId) {
        if (Objects.isNull(sourceId)) {
            return storageId;
        }
        if (!ModelLayer.ODS.equals(dataModel.getLayer())) {
            return getStorageId(dataModel, sourceId);
        }

        // ODS 且 sourceId 不为 null
        DataModel sourceDataModel = dataModelMapper.selectById(sourceId);
        ExecuteModeEnum executeMode = sourceDataModel.getScheduleConfig().getExecuteMode();
        if (ExecuteModeEnum.REALTIME.equals(executeMode)) {
            // 流处理
            OperatorPipeline operatorPipeline = operatorPipelineMapper.selectByDataModelId(sourceId);
            if (Objects.nonNull(operatorPipeline)) {
                DataStorage storage = dataStorageMapper.selectByDataModelIdAndConnectionIdWithConnection(
                    dataModel.getId(), operatorPipeline.getDataSourceConnectionId());
                log.info("ODS获取流处理存储: {}", storage);
                return Objects.nonNull(storage) ? storage.getId() : storageId;
            }
            return storageId;
        }
        // 批处理
        BatchArrangement batchArrangement = batchArrangementMapper.selectByDataModelId(sourceId);
        if (Objects.nonNull(batchArrangement) && ArrangeDisplayType.CANVAS.equals(batchArrangement.getDisplayType())) {
            return batchOperatorMapper.selectConnectionStorageByDataModelId(sourceId, ModelLayer.ODS,
                dataModel.getId());
        }
        // 代码模式暂时取第一个
        log.info("存储点来源 批处理代码模式");
        return dataModel.getDataStorages().get(0).getId();
    }

    private Integer getStorageId(DataModel dataModel, Integer sourceId) {
        // 判断是否为流处理
        if (Objects.nonNull(dataModel.getScheduleConfig())
            && ExecuteModeEnum.REALTIME.equals(dataModel.getScheduleConfig().getExecuteMode())) {
            OperatorPipeline pipeline = operatorPipelineMapper.selectByDataModelId(dataModel.getId());
            if (pipeline == null) {
                log.info("数据建模【{}】的来源数据建模【{}】未配置流处理任务", dataModel.getZhName(),
                    dataModel.getZhName());
                return null;
            }
            List<Operator> storageOperators = pipeline.getOperators().stream()
                .filter(e -> "doSeatunnelStorage".equalsIgnoreCase(e.getAbility().getEnName()))
                .toList();
            if (storageOperators.isEmpty()) {
                log.info("未配置存储算子");
                return null;
            }
            Integer currentStorageId = storageOperators.get(0).getStorageId();
            DataStorage storage = dataStorageMapper.selectById(currentStorageId);
            Integer storageId = storage != null ? storage.getId() : null;
            log.info("获取流处理任务数据来源存储点: {}, storageId: {}", dataModel.getZhName(), storageId);
            return storageId;
        }
        // 批处理
        Integer storageId = batchOperatorMapper.selectConnectionStorageByDataModelId(sourceId, null, dataModel.getId());
        log.info("获取批处理任务数据来源存储点: {}, storageId: {}", dataModel.getZhName(), storageId);
        return storageId;
    }

    private void handleTheme(DataLineageDetailVO detailVO, DataModel dataModel, ProcessTimeRange timeRange,
        Integer storageId) {
        if (ExecuteModeEnum.REALTIME.equals(dataModel.getScheduleConfig().getExecuteMode())) {
            // 流处理
            long processCount = themeStatisticsMapper.selectByDataModelId(dataModel.getId(),
                timeRange.getStartTime(), timeRange.getEndTime(), StatisticsDwdType.STREAM, null);
            detailVO.setProcessCount(processCount);
            long storageCount = themeStatisticsMapper.selectByDataModelId(dataModel.getId(),
                timeRange.getStartTime(), timeRange.getEndTime(), StatisticsDwdType.STORAGE, storageId);
            detailVO.setStorageCount(storageCount);
        } else {
            // 批处理 任务执行数量
            long processCount = themeStatisticsMapper.selectByDataModelId(dataModel.getId(),
                timeRange.getStartTime(), timeRange.getEndTime(), StatisticsDwdType.BATCH, storageId);
            detailVO.setProcessCount(processCount);
            long storageCount = themeStatisticsMapper.selectByDataModelId(dataModel.getId(),
                timeRange.getStartTime(), timeRange.getEndTime(), StatisticsDwdType.STORAGE, storageId);
            detailVO.setStorageCount(storageCount);
        }
        log.info("themeDetailVo: {} ", detailVO);
    }

    private void handleSubject(DataLineageDetailVO detailVO, DataModel dataModel, ProcessTimeRange timeRange,
        Integer storageId) {
        if (ExecuteModeEnum.REALTIME.equals(dataModel.getScheduleConfig().getExecuteMode())) {
            // 流处理
            long processCount = subjectStatisticsMapper.selectByDataModelId(dataModel.getId(),
                timeRange.getStartTime(), timeRange.getEndTime(), StatisticsDwdType.STREAM, null);
            detailVO.setProcessCount(processCount);
            long storageCount = subjectStatisticsMapper.selectByDataModelId(dataModel.getId(),
                timeRange.getStartTime(), timeRange.getEndTime(), StatisticsDwdType.STORAGE, storageId);
            detailVO.setStorageCount(storageCount);
        } else {
            // 批处理 任务执行数量
            long processCount = subjectStatisticsMapper.selectByDataModelId(dataModel.getId(),
                timeRange.getStartTime(), timeRange.getEndTime(), StatisticsDwdType.BATCH, storageId);
            detailVO.setProcessCount(processCount);
            long storageCount = subjectStatisticsMapper.selectByDataModelId(dataModel.getId(),
                timeRange.getStartTime(), timeRange.getEndTime(), StatisticsDwdType.STORAGE, storageId);
            detailVO.setStorageCount(storageCount);
        }
        log.info("subjectDetailVo: {} ", detailVO);
    }

    private void handleDwd(DataLineageDetailVO detailVO, DataModel dataModel, ProcessTimeRange timeRange,
        Integer storageId) {
        if (ExecuteModeEnum.REALTIME.equals(dataModel.getScheduleConfig().getExecuteMode())) {
            // 流处理
            long processCount = dwdStatisticsMapper.selectByDataModelId(dataModel.getId(),
                timeRange.getStartTime(), timeRange.getEndTime(), StatisticsDwdType.STREAM, null);
            detailVO.setProcessCount(processCount);
            long storageCount = dwdStatisticsMapper.selectByDataModelId(dataModel.getId(),
                timeRange.getStartTime(), timeRange.getEndTime(), StatisticsDwdType.STORAGE, storageId);
            detailVO.setStorageCount(storageCount);
        } else {
            // 批处理 任务执行数量
            long processCount = dwdStatisticsMapper.selectByDataModelId(dataModel.getId(),
                timeRange.getStartTime(), timeRange.getEndTime(), StatisticsDwdType.BATCH, storageId);
            detailVO.setProcessCount(processCount);
            long storageCount = dwdStatisticsMapper.selectByDataModelId(dataModel.getId(),
                timeRange.getStartTime(), timeRange.getEndTime(), StatisticsDwdType.STORAGE, storageId);
            detailVO.setStorageCount(storageCount);
        }
        log.info("dwdDetailVo: {} ", detailVO);
    }

    private void handleOds(DataLineageDetailVO detailVO, DataModel dataModel, ProcessTimeRange timeRange,
        Integer storageId) {
        DataLineageCountVO odsCountVo = odsStorageStatisticsMapper.selectByDataModelId(dataModel.getId(),
            timeRange.getStartTime(), timeRange.getEndTime(), storageId);
        log.info("odsCountVo : {}", odsCountVo);
        detailVO.setAccessCount(odsCountVo.getTotalCount());
        detailVO.setStorageCount(odsCountVo.getSuccessCount());
    }

    /**
     * 构建 sourceId -> DataModel 的映射
     *
     * @param id         id
     * @param dataSource 数据源
     * @return {@link Map }<{@link Integer }, {@link DataModel }>
     * <AUTHOR>
     * @since 2025/01/17 17:30:56
     */
    @NotNull
    private Map<Integer, DataModel> buildSource2ModelMap(Integer id, List<DataSourceConfig> dataSource) {
        // 如果 dataSource 为空，直接返回空 map
        if (ObjectUtils.isEmpty(dataSource)) {
            return Collections.emptyMap();
        }
        // 提前构建所有需要查询的 sourceId 列表
        List<Integer> sourceIds = dataSource.stream().map(DataSourceConfig::getSourceModelId).filter(Objects::nonNull)
            // 排除当前id
            .filter(sourceId -> !sourceId.equals(id)).toList();
        // 如果 sourceIds 为空，返回空 map
        if (ObjectUtils.isEmpty(sourceIds)) {
            return Collections.emptyMap();
        }
        // 批量查询 sourceId 对应的 DataModel
        List<DataModel> dataModels = dataModelMapper.selectByIdsWithStorage(sourceIds);
        // 将查询结果转为 Map
        return dataModels.stream().collect(Collectors.toMap(DataModel::getId, Function.identity()));
    }


}
