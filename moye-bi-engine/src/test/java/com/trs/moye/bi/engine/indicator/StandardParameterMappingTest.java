package com.trs.moye.bi.engine.indicator;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.trs.moye.base.data.indicator.entity.IndicatorApplicationFieldConfig;
import com.trs.moye.bi.engine.indicator.context.ParameterContext;
import com.trs.moye.bi.engine.indicator.entity.CalculationFunctionDefinition;
import com.trs.moye.bi.engine.indicator.entity.CalculationParameter;
import com.trs.moye.bi.engine.indicator.function.CalculationFunctionRegistry;
import com.trs.moye.bi.engine.indicator.function.ParameterResolver;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

/**
 * 标准参数映射测试类
 * <p>
 * 验证除了复杂参数映射场景之外的其他函数的参数解析逻辑，重点关注：
 * <ol>
 *   <li>getTrendData 函数的重载方法参数合并</li>
 *   <li>算术运算函数的标准参数映射</li>
 *   <li>参数名称与注解定义的一致性</li>
 *   <li>legacyIndex 属性的正确设置</li>
 *   <li>参数验证逻辑的正确性</li>
 * </ol>
 * </p>
 *
 * <AUTHOR>
 * @since 2025/07/29
 */
@DisplayName("标准参数映射测试")
class StandardParameterMappingTest {

    /**
     * 测试前的初始化
     * <p>
     * 确保函数注册表在测试执行前已正确初始化。
     * </p>
     */
    @BeforeEach
    void setUp() {
        // 确保函数注册表已初始化
        CalculationFunctionRegistry.ensureInitialized();
    }

    /**
     * 测试 getTrendData 函数的重载方法参数合并
     * <p>
     * 验证 getTrendData 函数的 1参数、4参数、5参数版本的参数映射是否正确， 以及可选参数的默认值处理是否正确。
     * </p>
     */
    @Test
    @DisplayName("测试 getTrendData 函数的重载方法参数合并")
    void testGetTrendDataOverloadParameterMerging() {
        Optional<CalculationFunctionDefinition> definitionOpt =
            CalculationFunctionRegistry.getDefinition("getTrendData");
        assertTrue(definitionOpt.isPresent(), "getTrendData 函数定义应该存在");

        CalculationFunctionDefinition definition = definitionOpt.get();

        // 验证参数合并结果
        List<CalculationParameter> parameters = definition.getParameters();
        assertNotNull(parameters, "参数列表不应为空");
        assertFalse(parameters.isEmpty(), "应该至少有1个参数");

        // 验证必需参数
        CalculationParameter targetFieldParam = parameters.stream()
            .filter(p -> "targetField".equals(p.getName()))
            .findFirst()
            .orElse(null);
        assertNotNull(targetFieldParam, "应该有 targetField 参数");
        assertTrue(targetFieldParam.isRequired(), "targetField 应该是必需参数");
        assertEquals(0, targetFieldParam.getLegacyIndex(), "targetField 的 legacyIndex 应该是 0");

        // 验证可选参数
        String[] optionalParamNames = {"upText", "downText", "equalText", "separator"};
        for (String paramName : optionalParamNames) {
            parameters.stream()
                .filter(p -> paramName.equals(p.getName()))
                .findFirst().ifPresent(param -> assertFalse(param.isRequired(), paramName + " 应该是可选参数"));
        }
    }

    /**
     * 测试 getTrendData 函数的不同参数版本解析
     * <p>
     * 验证 getTrendData 函数在不同参数数量下的解析正确性。
     * </p>
     *
     * @param paramString param 字符串
     * @param description 描述
     * <AUTHOR>
     * @since 2025/07/30 23:52:45
     */
    @ParameterizedTest
    @DisplayName("测试 getTrendData 函数的不同参数版本解析")
    @CsvSource({
        "'field1', 1参数版本",
        "'field1,↑,↓,—', 4参数版本",
        "'field1,↑,↓,—,comma', 5参数版本"
    })
    void testGetTrendDataParameterVersions(String paramString, String description) {
        List<String> parameters = Arrays.asList(paramString.split(","));

        Optional<CalculationFunctionDefinition> definitionOpt =
            CalculationFunctionRegistry.getDefinition("getTrendData");
        assertTrue(definitionOpt.isPresent(), "getTrendData 函数定义应该存在");

        CalculationFunctionDefinition definition = definitionOpt.get();

        IndicatorApplicationFieldConfig config = new IndicatorApplicationFieldConfig();
        config.setFunction("getTrendData");
        config.setInput(parameters);

        // 验证参数解析成功
        assertDoesNotThrow(() -> {
            ParameterContext context = ParameterResolver.resolve(definition, config);
            assertNotNull(context, description + " 的参数上下文不应为空");
            assertTrue(context.getParameterCount() > 0, description + " 应该解析出参数");

            // 验证 targetField 参数
            assertTrue(context.hasParameter("targetField"), "应该有 targetField 参数");
            assertEquals("field1", context.getString("targetField"), "targetField 值应该正确");

            // 验证必需参数都存在
            for (CalculationParameter param : definition.getParameters()) {
                if (param.isRequired()) {
                    assertTrue(context.hasParameter(param.getName()),
                        "必需参数应该存在: " + param.getName());
                }
            }
        }, "getTrendData " + description + " 参数解析应该成功");
    }

    /**
     * 测试算术运算函数的标准参数映射
     * <p>
     * 验证 add、subtract、multiply、divide 函数的参数解析是否正确， 确认它们使用标准的 resolveStandardLegacyParameters 方法。
     * </p>
     *
     * @param functionName 函数名称
     * @param paramString  param 字符串
     * @param description  描述
     * <AUTHOR>
     * @since 2025/07/30 23:52:42
     */
    @ParameterizedTest
    @DisplayName("测试算术运算函数的标准参数映射")
    @CsvSource({
        "add, 'field1,field2,field3', 加法运算",
        "subtract, 'field1,field2', 减法运算",
        "multiply, 'field1,field2,field3', 乘法运算",
        "divide, 'field1,field2', 除法运算"
    })
    void testArithmeticFunctionParameterMapping(String functionName, String paramString, String description) {

        Optional<CalculationFunctionDefinition> definitionOpt =
            CalculationFunctionRegistry.getDefinition(functionName);
        assertTrue(definitionOpt.isPresent(), functionName + " 函数定义应该存在");

        CalculationFunctionDefinition definition = definitionOpt.get();

        // 验证参数定义
        List<CalculationParameter> paramDefs = definition.getParameters();
        assertNotNull(paramDefs, functionName + " 的参数定义不应为空");
        assertFalse(paramDefs.isEmpty(), functionName + " 应该有参数定义");

        // 验证第一个参数是 fields
        CalculationParameter firstParam = paramDefs.get(0);
        assertEquals("fields", firstParam.getName(), functionName + " 的第一个参数应该是 fields");
        assertTrue(firstParam.isRequired(), functionName + " 的 fields 参数应该是必需的");
        assertEquals(0, firstParam.getLegacyIndex(), functionName + " 的 fields 参数的 legacyIndex 应该是 0");

        List<String> parameters = Arrays.asList(paramString.split(","));
        // 测试参数解析
        IndicatorApplicationFieldConfig config = new IndicatorApplicationFieldConfig();
        config.setFunction(functionName);
        config.setInput(parameters);

        assertDoesNotThrow(() -> {
            ParameterContext context = ParameterResolver.resolve(definition, config);
            assertNotNull(context, functionName + " 的参数上下文不应为空");
            assertTrue(context.getParameterCount() > 0, functionName + " 应该解析出参数");

            // 验证 fields 参数
            assertTrue(context.hasParameter("fields"), functionName + " 应该有 fields 参数");
            Object fieldsValue = context.getObject("fields");
            assertNotNull(fieldsValue, functionName + " 的 fields 参数值不应为空");

            // 验证必需参数都存在
            for (CalculationParameter param : definition.getParameters()) {
                if (param.isRequired()) {
                    assertTrue(context.hasParameter(param.getName()),
                        functionName + " 的必需参数应该存在: " + param.getName());
                }
            }
        }, functionName + " " + description + " 参数解析应该成功");
    }

    /**
     * 测试命名参数方式的兼容性
     * <p>
     * 验证新的命名参数方式是否能正确工作。
     * </p>
     */
    @Test
    @DisplayName("测试命名参数方式的兼容性")
    void testNamedParameterCompatibility() {
        // 测试 getTrendData 的命名参数
        Map<String, String> namedParams = new HashMap<>();
        namedParams.put("targetField", "testField");
        namedParams.put("upText", "上升");
        namedParams.put("downText", "下降");

        Optional<CalculationFunctionDefinition> definitionOpt =
            CalculationFunctionRegistry.getDefinition("getTrendData");
        assertTrue(definitionOpt.isPresent(), "getTrendData 函数定义应该存在");

        CalculationFunctionDefinition definition = definitionOpt.get();

        IndicatorApplicationFieldConfig config = new IndicatorApplicationFieldConfig();
        config.setFunction("getTrendData");
        config.setNamedParameters(namedParams);

        assertDoesNotThrow(() -> {
            ParameterContext context = ParameterResolver.resolve(definition, config);
            assertNotNull(context, "命名参数的参数上下文不应为空");

            // 验证参数值
            assertEquals("testField", context.getString("targetField"));
            assertEquals("上升", context.getString("upText"));
            assertEquals("下降", context.getString("downText"));
        }, "getTrendData 命名参数解析应该成功");
    }

    /**
     * 测试参数验证逻辑的正确性
     * <p>
     * 验证 validateRequiredParameters 方法能够正确验证所有必需参数。
     * </p>
     */
    @Test
    @DisplayName("测试参数验证逻辑的正确性")
    void testParameterValidationLogic() {
        String[] testFunctions = {"getTrendData", "add", "subtract", "multiply", "divide"};

        for (String functionName : testFunctions) {
            Optional<CalculationFunctionDefinition> definitionOpt =
                CalculationFunctionRegistry.getDefinition(functionName);
            assertTrue(definitionOpt.isPresent(), functionName + " 函数定义应该存在");

            CalculationFunctionDefinition definition = definitionOpt.get();

            // 准备最小参数集合
            List<String> minimalParams = getMinimalParametersForFunction(functionName);

            IndicatorApplicationFieldConfig config = new IndicatorApplicationFieldConfig();
            config.setFunction(functionName);
            config.setInput(minimalParams);

            // 验证参数解析和验证都能成功
            assertDoesNotThrow(() -> {
                ParameterContext context = ParameterResolver.resolve(definition, config);
                assertNotNull(context, functionName + " 的参数上下文不应为空");

                // 验证所有必需参数都存在
                for (CalculationParameter param : definition.getParameters()) {
                    if (param.isRequired()) {
                        assertTrue(context.hasParameter(param.getName()),
                            functionName + " 的必需参数应该存在: " + param.getName());
                    }
                }
            }, functionName + " 的参数验证应该成功");
        }
    }

    /**
     * 测试 legacyIndex 属性的正确设置
     * <p>
     * 验证所有函数的参数 legacyIndex 属性是否正确设置。
     * </p>
     *
     * @param functionName 函数名称
     */
    @ParameterizedTest
    @DisplayName("测试 legacyIndex 属性的正确设置")
    @CsvSource({
        "getTrendData",
        "add",
        "subtract",
        "multiply",
        "divide"
    })
    void testLegacyIndexCorrectness(String functionName) {
        Optional<CalculationFunctionDefinition> definitionOpt =
            CalculationFunctionRegistry.getDefinition(functionName);
        assertTrue(definitionOpt.isPresent(), functionName + " 函数定义应该存在");

        CalculationFunctionDefinition definition = definitionOpt.get();
        List<CalculationParameter> parameters = definition.getParameters();

        // 验证 legacyIndex 的连续性和正确性
        for (CalculationParameter param : parameters) {
            int legacyIndex = param.getLegacyIndex();
            assertTrue(legacyIndex >= 0,
                functionName + " 的参数 " + param.getName() + " 的 legacyIndex 应该大于等于0");

            // 验证 legacyIndex 在合理范围内
            assertTrue(legacyIndex < parameters.size(),
                functionName + " 的参数 " + param.getName() + " 的 legacyIndex 应该小于参数总数");
        }
    }

    /**
     * 辅助方法：获取函数的最小参数集合
     *
     * @param functionName 函数名称
     * @return {@link List }<{@link String }>
     */
    private List<String> getMinimalParametersForFunction(String functionName) {
        return switch (functionName) {
            case "add", "multiply", "subtract", "divide" -> Arrays.asList("field1", "field2");
            default -> List.of("field1");
        };
    }
}
