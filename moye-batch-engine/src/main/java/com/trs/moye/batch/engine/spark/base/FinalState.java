package com.trs.moye.batch.engine.spark.base;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import org.apache.spark.launcher.SparkAppHandle.State;


/**
 * 任务的结束状态枚举类 SUCCESS spark任务无异常 FAILED spark任务失败
 */
public enum FinalState {

    /**
     * 执行成功
     */
    SUCCESS(State.FINISHED),

    /**
     * 执行失败
     */
    FAILED(State.FAILED),

    /**
     * 监听断联
     */
    LOST(State.LOST),

    /**
     * 取消执行
     */
    KILLED(State.KILLED),

    /**
     * 执行中
     */
    RUNNING(State.CONNECTED, State.SUBMITTED, State.RUNNING),

    /**
     * 未知
     */
    UNKNOWN(State.UNKNOWN);


    private final Set<State> correspondingStates;

    FinalState(State... states) {
        this.correspondingStates = new HashSet<>(Arrays.asList(states));
    }

    /**
     * 根据 spark state 转化为 中台需要的 state
     *
     * @param state {@link State}
     * @return {@link FinalState}
     */
    public static FinalState from(State state) {
        for (FinalState finalState : FinalState.values()) {
            if (finalState.correspondingStates.contains(state)) {
                return finalState;
            }
        }
        return SUCCESS;
    }
}
