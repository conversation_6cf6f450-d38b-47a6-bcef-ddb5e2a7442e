package com.trs.moye.base.data.schedule;

import com.trs.moye.base.data.execute.ExecuteModeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 实时调度信息
 *
 * <AUTHOR>
 * @since 2024-09-30 13:46
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class RealtimeScheduleInfo extends ScheduleInfo {

    @Override
    public XxlJobScheduleConfig toXxlJobScheduleConfig() {
        throw new UnsupportedOperationException("实时调度不支持转xxl-job调度");
    }

    public RealtimeScheduleInfo(ExecuteModeEnum executeModeEnum) {
        setExecuteMode(executeModeEnum);
    }
}
