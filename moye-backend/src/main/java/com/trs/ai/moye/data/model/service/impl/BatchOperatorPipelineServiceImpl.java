package com.trs.ai.moye.data.model.service.impl;

import com.trs.ai.moye.data.ability.dao.AbilityMapper;
import com.trs.ai.moye.data.model.dao.BatchArrangementMapper;
import com.trs.ai.moye.data.model.dao.batch.BatchOperatorMapper;
import com.trs.moye.base.data.model.entity.Canvas;
import com.trs.ai.moye.data.model.dto.arrangement.batch.BatchOperatorDTO;
import com.trs.ai.moye.data.model.dto.arrangement.batch.BatchOperatorPipelineDTO;
import com.trs.ai.moye.data.model.entity.BatchArrangement;
import com.trs.ai.moye.data.model.service.BatchOperatorPipelineService;
import com.trs.moye.ability.entity.Ability;
import com.trs.moye.ability.entity.InputBind;
import com.trs.moye.ability.entity.Schema;
import com.trs.moye.ability.entity.operator.BatchOperator;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.entity.DataSourceConfig;
import com.trs.moye.base.data.source.dao.DataSourceConfigMapper;
import com.trs.moye.base.data.storage.dao.DataStorageMapper;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * BatchOperatorPipelineServiceImpl
 *
 * <AUTHOR>
 * @since 2025/3/24 17:26
 */
@Service
public class BatchOperatorPipelineServiceImpl implements BatchOperatorPipelineService {

    @Resource
    private BatchArrangementMapper batchArrangementMapper;
    @Resource
    private DataSourceConfigMapper dataSourceConfigMapper;
    @Resource
    private DataModelMapper dataModelMapper;
    @Resource
    private DataStorageMapper dataStorageMapper;
    @Resource
    private BatchOperatorMapper batchOperatorMapper;
    @Resource
    private ArrangementFieldService arrangementFieldService;
    @Resource
    private AbilityMapper abilityMapper;

    /**
     * 保存编排
     *
     * @param dataModelId              建模id
     * @param batchOperatorPipelineDTO 算子编排
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatchOperatorPipeline(Integer dataModelId, BatchOperatorPipelineDTO batchOperatorPipelineDTO) {
        // 校验算子的输入参数绑定方式 是否满足 Ability inputSchema 的 bindType
        checkAbility(batchOperatorPipelineDTO.getOperators());

        // DAG模式的数据建模的数据来源 需要根据算子编排的输入算子中选择的来源表 汇总得到
        // 更新 数据来源
        updateDataSource(dataModelId, batchOperatorPipelineDTO.getOperators());

        // 更新 元数据
        updateFields(dataModelId, batchOperatorPipelineDTO.getOperators());

        // batchArrangement在创建要素表时已经同时创建
        BatchArrangement batchArrangement = batchArrangementMapper.selectByDataModelId(dataModelId);
        // 存储画布信息
        batchArrangement.setCanvas(batchOperatorPipelineDTO.getCanvas());
        batchArrangementMapper.updateById(batchArrangement);

        // 算子信息处理后存到算子表中
        updateArrangement(batchArrangement.getId(), dataModelId, batchOperatorPipelineDTO.getOperators());

        dataModelMapper.updateIsArranged(dataModelId, true);
    }

    private void checkAbility(List<BatchOperatorDTO> operators) {
        if (CollectionUtils.isEmpty(operators)) {
            return;
        }
        // 获取所有能力id
        Set<Integer> abilityIds = operators.stream().map(BatchOperatorDTO::getAbilityId).filter(Objects::nonNull)
            .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(abilityIds)) {
            return;
        }
        // 查询能力
        Map<Integer, Ability> abilityMap = abilityMapper.selectBatchIds(abilityIds).stream()
            .collect(Collectors.toMap(Ability::getId, ability -> ability));

        for (BatchOperatorDTO operator : operators) {
            Integer abilityId = operator.getAbilityId();
            if (abilityId == null) {
                continue;
            }

            if (!abilityMap.containsKey(abilityId)) {
                throw new BizException("Ability not found with ability id %s for operator display id %s",
                    operator.getAbilityId(), operator.getDisplayId());
            }

            // 校验 inputBind 和 inputSchema 是否匹配
            InputBind inputBind = operator.getInputBind();
            Schema inputSchema = abilityMap.get(abilityId).getInputSchema();

            validateSchemaAndInputBind(inputSchema, inputBind);
        }
    }

    private static void validateSchemaAndInputBind(Schema inputSchema, InputBind inputBind) {
        if (inputSchema == null || inputBind == null) {
            return;
        }

        // 有绑定类型限制并且字段有绑定，绑定类型不一致的将抛出异常
        if (inputSchema.getBindType() != null && inputBind.getBinding(inputSchema.getEnName()) != null
            && inputBind.getBinding(inputSchema.getEnName()).getBindType() != null
            && !inputSchema.getBindType().equals(inputBind.getBinding(inputSchema.getEnName()).getBindType())) {
            throw new BizException("BindType mismatch for field '%s', input schema bindType=%s",
                inputSchema.getEnName(), inputSchema.getBindType());
        }

        // 递归
        if (inputSchema instanceof Schema.ObjectTypeSchema objectTypeSchema) {
            Map<String, Schema> properties = objectTypeSchema.getProperties();
            for (Schema schema : properties.values()) {
                validateSchemaAndInputBind(schema, inputBind);
            }
        } else if (inputSchema instanceof Schema.ArrayTypeSchema arrayTypeSchema) {
            validateSchemaAndInputBind(arrayTypeSchema.getItems(), inputBind);
        }
    }

    private void updateFields(Integer dataModelId, List<BatchOperatorDTO> operators) {
        // 存储算子的输出字段同步到数据建模字段中
        BatchOperatorDTO finalOperator = operators.stream()
            .filter(operator -> dataModelId.equals(operator.getDataModelId())).findFirst()
            .orElse(new BatchOperatorDTO());

        if (Objects.nonNull(finalOperator.getOutputFields()) && !finalOperator.getOutputFields().isEmpty()) {
            arrangementFieldService.addDataModelFields(dataModelId, finalOperator.getOutputFields().toFields());
        }
    }

    private void updateDataSource(Integer dataModelId, List<BatchOperatorDTO> operators) {
        // 完全清理旧的数据来源
        dataSourceConfigMapper.deleteByDataModelId(dataModelId);

        // TABLE 类型，并且算子的 dataModelId 不是当前数据建模, 即输入算子
        Set<Integer> dataStorageIds = operators.stream()
            .filter(operator -> operator.getType().isTable() && !operator.getDataModelId().equals(dataModelId))
            .map(BatchOperatorDTO::getStorageId).collect(Collectors.toSet());

        List<DataSourceConfig> dataSourceConfigs = dataStorageMapper.selectBatchIds(dataStorageIds).stream()
            .map(dataStorage -> DataSourceConfig.from(dataStorage, dataModelId)).toList();

        dataSourceConfigMapper.insert(dataSourceConfigs);
    }

    private void updateArrangement(Integer arrangementId, Integer dataModelId, List<BatchOperatorDTO> dtoList) {
        List<BatchOperator> operatorList = dtoList.stream()
            .map(operator -> operator.toBatchOperator(arrangementId, dataModelId))
            .toList();
        BatchOperator.assignParentDisplayIds(operatorList);
        batchOperatorMapper.deleteByArrangementId(arrangementId);
        batchOperatorMapper.insert(operatorList);
    }

    /**
     * 查询编排
     *
     * @param dataModelId 建模id
     * @return {@link BatchOperatorPipelineDTO }
     */
    @Override
    public BatchOperatorPipelineDTO getBatchOperatorPipeline(Integer dataModelId) {
        // 查询编排
        BatchArrangement batchArrangement = batchArrangementMapper.selectByDataModelId(dataModelId);
        if (batchArrangement == null) {
            return new BatchOperatorPipelineDTO(new Canvas(), List.of());
        }

        List<BatchOperator> operators = batchOperatorMapper.selectByArrangementId(batchArrangement.getId());
        List<BatchOperatorDTO> dto = operators.stream().map(BatchOperatorDTO::fromBatchOperator).toList();

        return new BatchOperatorPipelineDTO(batchArrangement.getCanvas(), dto);
    }
}
