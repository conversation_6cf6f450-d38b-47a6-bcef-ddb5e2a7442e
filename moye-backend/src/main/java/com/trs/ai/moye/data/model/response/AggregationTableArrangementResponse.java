package com.trs.ai.moye.data.model.response;

import com.trs.ai.moye.common.utils.BeanUtil;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.data.model.entity.AggregationTableArrangement;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025-08-01 18:34
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class AggregationTableArrangementResponse extends AggregationTableArrangement {

    private ModelLayer modelLayer;

    public AggregationTableArrangementResponse(AggregationTableArrangement parent, ModelLayer modelLayer) {
        BeanUtil.copyInheritProperties(parent, this);
        this.modelLayer = modelLayer;
    }
}
