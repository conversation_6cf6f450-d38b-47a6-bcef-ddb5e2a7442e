package com.trs.ai.moye.data.model.service.impl;

import com.trs.ai.moye.common.utils.BizUtils;
import com.trs.ai.moye.data.model.response.ScheduleConfigResponse;
import com.trs.ai.moye.data.model.service.DataAccessMonitorConfigService;
import com.trs.ai.moye.data.model.service.DataModelScheduleService;
import com.trs.ai.moye.xxljob.XXLJobManager;
import com.trs.ai.moye.xxljob.entity.MoyeXxlJob;
import com.trs.ai.moye.xxljob.enums.AppJobHandler;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.utils.AssertUtils;
import com.trs.moye.base.data.execute.ExecuteModeEnum;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.dao.DataModelScheduleConfigMapper;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.entity.DataModelScheduleConfig;
import com.trs.moye.base.data.schedule.ScheduleInfo;
import com.trs.moye.base.data.schedule.ScheduleTypeEnum;
import com.trs.moye.base.monitor.dao.DataModelMonitorConfigMapper;
import com.trs.moye.base.monitor.entity.DataModelMonitorConfig;
import com.trs.moye.base.xxljob.XxlJobInfo;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 数据建模-调度信息
 *
 * <AUTHOR>
 * @since 2025/1/3 16:23
 */
@Service
@Slf4j
public class DataModelScheduleServiceImpl implements DataModelScheduleService {

    @Resource
    private DataModelMapper dataModelMapper;

    @Resource
    private DataModelScheduleConfigMapper dataModelScheduleConfigMapper;

    @Resource
    private XXLJobManager xxlJobManager;

    @Resource
    private DataModelMonitorConfigMapper dataModelMonitorConfigMapper;

    @Resource
    private DataAccessMonitorConfigService dataModelConfigService;

    /**
     * 流处理接入是伪实时，默认调度间隔为300s
     */
    private static final String XXL_JOB_REAL_TIME_FIX_RATE = "300";

    public static final String MSG_DATA_MODEL_NOT_EXIST = "主键为【%s】的数据建模不存在";


    @Override
    public ScheduleConfigResponse getModelScheduleInfo(Integer id) {
        DataModelScheduleConfig scheduleConfig = modelScheduleConfig(id);
        return new ScheduleConfigResponse(scheduleConfig);
    }

    private DataModelScheduleConfig modelScheduleConfig(Integer id) {
        DataModel dataModel = dataModelMapper.selectById(id);
        AssertUtils.notEmpty(dataModel, MSG_DATA_MODEL_NOT_EXIST, id);
        return modelScheduleConfig(dataModel);
    }

    private DataModelScheduleConfig modelScheduleConfig(DataModel dataModel) {
        DataModelScheduleConfig scheduleConfig = dataModelScheduleConfigMapper.selectByDataModelId(dataModel.getId());
//        AssertUtils.notEmpty(scheduleConfig, "【%s】数据建模的调度配置不存在", BizUtils.getDataModelIdentity(dataModel));
        return scheduleConfig;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateModelScheduleConfig(Integer dataModelId, ScheduleInfo scheduleInfo) {
        DataModel dataModel = dataModelMapper.getById(dataModelId);
        DataModelScheduleConfig scheduleConfig = modelScheduleConfig(dataModel);
        ScheduleInfo oldScheduleInfo = scheduleConfig.getScheduleInfo();
        if (oldScheduleInfo.getExecuteMode() == ExecuteModeEnum.REALTIME
            && scheduleInfo.getExecuteMode() != ExecuteModeEnum.REALTIME) {
            throw new BizException("【%s】实时建模不允许修改调度配置为非实时", dataModel.getZhName());
        }
        createDefaultTaskExecutionTimeConfig(dataModelId, scheduleConfig.getExecuteMode());
        scheduleConfig.setScheduleInfo(scheduleInfo);

        MoyeXxlJob modelXxlJob = buildModelMoyeXxlJob(dataModel, scheduleInfo);
        Integer jobId = xxlJobManager.updateJob(scheduleConfig.getXxlJobId(), modelXxlJob);
        scheduleConfig.setXxlJobId(jobId);
        dataModelScheduleConfigMapper.updateById(scheduleConfig);
    }

    @Override
    public void createScheduleConfig(Integer dataModelId, ScheduleInfo scheduleInfo) {
        DataModel dataModel = dataModelMapper.getById(dataModelId);
        createScheduleConfig(dataModel, scheduleInfo);
    }

    @Override
    public void createScheduleConfig(DataModel dataModel, ScheduleInfo scheduleInfo) {
        DataModelScheduleConfig scheduleConfig = new DataModelScheduleConfig(dataModel.getId(), scheduleInfo);
        MoyeXxlJob moyeXxlJob = buildModelMoyeXxlJob(dataModel, scheduleConfig.getScheduleInfo());
        XxlJobInfo job = xxlJobManager.createJob(moyeXxlJob);
        scheduleConfig.setXxlJobId(job.getId());
        dataModelScheduleConfigMapper.insert(scheduleConfig);
    }

    private MoyeXxlJob buildModelMoyeXxlJob(DataModel dataModel, ScheduleInfo scheduleInfo) {
        if (scheduleInfo.getExecuteMode() == ExecuteModeEnum.REALTIME) {
            return buildRealtimeXxlJob(dataModel);
        } else {
            return BizUtils.getModelXxlJob(dataModel, scheduleInfo);
        }
    }

    private MoyeXxlJob buildRealtimeXxlJob(DataModel dataModel) {
        AppJobHandler appJobHandler = AppJobHandler.getRealTimeJobHandler(dataModel.getLayer());
        return MoyeXxlJob.builder()
            .jobName(dataModel.buildModelTaskName())
            .jobParam(dataModel.buildXxlTaskParam())
            .app(appJobHandler.getApp())
            .jobHandler(appJobHandler.getJobHandler())
            .scheduleType(ScheduleTypeEnum.FIX_RATE)
            .scheduleConf(XXL_JOB_REAL_TIME_FIX_RATE).build();
    }

    @Override
    public void deleteScheduleConfig(Integer id) {
        DataModelScheduleConfig scheduleConfig = dataModelScheduleConfigMapper.selectByDataModelId(id);
        if (ObjectUtils.isEmpty(scheduleConfig)) {
            log.warn("主键为【{}】贴源库未配置调度信息", id);
            return;
        }
        // 按道理xxlJobId非空，但是实时处理的xxl-job是后加的，数据库存在脏数据，先添加一个非空判断避免报错
        if (ObjectUtils.isNotEmpty(scheduleConfig.getXxlJobId())) {
            xxlJobManager.deleteJob(scheduleConfig.getXxlJobId());
        }
        dataModelScheduleConfigMapper.deleteByDataModelId(id);
    }

    /**
     * 创建默认任务执行时间配置 当调度配置更新时，需要为非实时模式创建默认的任务执行时间监控配置
     *
     * @param dataModelId     数据建模ID
     * @param executeModeEnum 执行模式枚举
     */
    private void createDefaultTaskExecutionTimeConfig(Integer dataModelId, ExecuteModeEnum executeModeEnum) {
        if (ExecuteModeEnum.REALTIME.equals(executeModeEnum)) {
            return;
        }
        DataModelMonitorConfig monitorConfig = DataModelMonitorConfig.buildBatchTaskMonitorConfig(dataModelId);
        monitorConfig.setXxlJobId(dataModelConfigService.createMonitorConfigXxlJob(monitorConfig));
        dataModelMonitorConfigMapper.insert(monitorConfig);
    }
}
