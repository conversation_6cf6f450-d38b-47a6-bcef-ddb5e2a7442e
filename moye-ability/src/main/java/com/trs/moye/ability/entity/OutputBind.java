package com.trs.moye.ability.entity;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.trs.moye.ability.entity.operator.OperatorRowType;
import com.trs.moye.ability.enums.AbilityFieldType;
import com.trs.moye.ability.enums.ResultBindMethod;
import com.trs.moye.ability.utils.ValueParseUtils;
import com.trs.moye.base.common.enums.FieldType;
import com.trs.moye.base.common.utils.JsonUtils;
import java.io.Serializable;
import java.util.AbstractMap.SimpleEntry;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 数据绑定工具 - 用于定义 JSON 字段与方法参数的绑定关系
 */
@Getter
public class OutputBind implements Serializable {

    private static final String JSON_PATH = "jsonPath";

    private static final String ROOT_FIELD = "$root$";

    private final Map<String, Binding> bindings = new LinkedHashMap<>();

    /**
     * 获取绑定信息
     *
     * @param paramName 方法参数名
     * @return 绑定信息
     */
    public Binding getBinding(String paramName) {
        return bindings.get(paramName);
    }

    /**
     * 转换为 JSON 字符串
     *
     * @return JSON 字符串
     */
    public String toString() {
        return JsonUtils.toJsonString(bindings);
    }

    /**
     * 添加替换绑定规则
     *
     * @param fieldName 输出字段名
     * @param jsonPath  JSON 路径
     * @return 当前对象
     */
    public OutputBind addReplaceBinding(String jsonPath, String fieldName) {
        bindings.put(fieldName, new ReplaceBinding(jsonPath, fieldName));
        return this;
    }

    /**
     * 添加追加绑定规则
     *
     * @param jsonPath  JSON路径
     * @param fieldName 方法参数名
     * @return 当前对象
     */
    public OutputBind addAppendBinding(String jsonPath, String fieldName) {
        bindings.put(fieldName, new AppendBinding(jsonPath, fieldName));
        return this;
    }

    /**
     * 添加数据拼接绑定规则
     *
     * @param jsonPath  JSON 路径
     * @param fieldName 方法参数名
     * @return 当前对象
     */
    public OutputBind addConcatBinding(String jsonPath, String fieldName) {
        bindings.put(fieldName, new ConcatBinding(jsonPath, fieldName));
        return this;
    }

    /**
     * 添加数据拼接绑定规则
     *
     * @return 当前对象
     */
    public OutputBind addRootBinding() {
        bindings.put(ROOT_FIELD, new RootBinding());
        return this;
    }

    /**
     * 绑定关系
     */
    @AllArgsConstructor
    @NoArgsConstructor
    @Getter
    @Setter
    public abstract static class Binding implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 绑定方法
         */
        protected ResultBindMethod method;
        /**
         * 绑定字段名
         */
        protected String fieldName;

        /**
         * 字段类型
         */
        protected AbilityFieldType fieldType;

        /**
         * 次序
         */
        protected Integer order;

        /**
         * 数组类型时的元素类型
         */
        protected AbilityFieldType itemType;

        /**
         * 对象类型时所具体对应的知识库类型
         */
        protected AbilityFieldObjectType objectType;

        protected Binding(ResultBindMethod method, String fieldName) {
            this.method = method;
            this.fieldName = fieldName;
        }

        /**
         * 获取 JSON 路径
         *
         * @return jsonPath
         */
        public abstract String getJsonPath();

        /**
         * 解析 JSON 字段值
         *
         * @param valueNode  JSON 字段值
         * @param resultNode 结果节点
         * @throws IllegalArgumentException 如果解析失败
         */
        public abstract void parseValue(ObjectNode valueNode, JsonNode resultNode) throws IllegalArgumentException;

    }

    /**
     * 替换
     */
    @Getter
    @NoArgsConstructor
    public static class ReplaceBinding extends Binding {

        /**
         * JSON 路径
         */
        String jsonPath;

        public ReplaceBinding(String jsonPath, String fieldName) {
            super(ResultBindMethod.REPLACE, fieldName);
            this.jsonPath = jsonPath;
        }

        public ReplaceBinding(String jsonPath, String fieldName, AbilityFieldType type, Integer order,
            AbilityFieldType itemType, AbilityFieldObjectType objectType) {
            super(ResultBindMethod.REPLACE, fieldName, type, order, itemType, objectType);
            this.jsonPath = jsonPath;
        }

        public ReplaceBinding(String jsonPath, String fieldName, AbilityFieldType type, Integer order,
            AbilityFieldType itemType) {
            super(ResultBindMethod.REPLACE, fieldName, type, order, itemType, null);
            this.jsonPath = jsonPath;
        }

        @Override
        public void parseValue(ObjectNode valueNode, JsonNode resultNode) {
            // 1. 解析目标路径（如解析 "b.target" 到父节点和字段名）
            NestedPathResult target = resolveNestedPath(valueNode, fieldName);
            // 2. 从源数据获取值（使用jsonPath定位）
            JsonNode sourceValue = ValueParseUtils.getSourceValueFromJsonPath(resultNode, jsonPath);
            // 3. 根据指定的字段类型转换值
            if (Objects.nonNull(fieldType)) {
                sourceValue = convertValueToType(sourceValue, fieldType, itemType);
            }
            // 4. 替换目标字段值
            target.parentNode().set(target.targetFieldName(), sourceValue);
        }
    }

    /**
     * 追加
     */
    @Getter
    @NoArgsConstructor
    public static class AppendBinding extends Binding {

        /**
         * 绑定字段中文名
         */
        protected String fieldZhName;

        /**
         * JSON 路径
         */
        String jsonPath;

        public AppendBinding(String jsonPath, String fieldName) {
            super(ResultBindMethod.APPEND, fieldName);
            this.jsonPath = jsonPath;
        }

        public AppendBinding(String jsonPath, String fieldName, String fieldZhName, AbilityFieldType type,
            Integer order, AbilityFieldType itemType, AbilityFieldObjectType objectType) {
            super(ResultBindMethod.APPEND, fieldName, type, order, itemType, objectType);
            this.jsonPath = jsonPath;
            this.fieldZhName = fieldZhName;
        }

        public AppendBinding(String jsonPath, String fieldName, String fieldZhName, AbilityFieldType type,
            Integer order, AbilityFieldType itemType) {
            super(ResultBindMethod.APPEND, fieldName, type, order, itemType, null);
            this.jsonPath = jsonPath;
            this.fieldZhName = fieldZhName;
        }

        @Override
        public void parseValue(ObjectNode valueNode, JsonNode resultNode) {
            NestedPathResult target = resolveNestedPath(valueNode, fieldName);
            // 从源数据获取值（使用jsonPath定位）
            JsonNode sourceValue = ValueParseUtils.getSourceValueFromJsonPath(resultNode, jsonPath);
            // 根据指定的字段类型转换值
            if (Objects.nonNull(fieldType)) {
                sourceValue = convertValueToType(sourceValue, fieldType, itemType);
            }
            // 替换目标字段值
            target.parentNode().set(target.targetFieldName(), sourceValue);
        }
    }

    /**
     * 拼接
     */
    @Getter
    @NoArgsConstructor
    public static class ConcatBinding extends Binding {

        /**
         * JSON 路径
         */
        String jsonPath;

        public ConcatBinding(String jsonPath, String fieldName) {
            super(ResultBindMethod.CONCAT, fieldName);
            this.jsonPath = jsonPath;
        }

        public ConcatBinding(String jsonPath, String fieldName, AbilityFieldType type, Integer order,
            AbilityFieldType itemType, AbilityFieldObjectType objectType) {
            super(ResultBindMethod.CONCAT, fieldName, type, order, itemType, objectType);
            this.jsonPath = jsonPath;
        }

        public ConcatBinding(String jsonPath, String fieldName, AbilityFieldType type, Integer order,
            AbilityFieldType itemType) {
            super(ResultBindMethod.CONCAT, fieldName, type, order, itemType, null);
            this.jsonPath = jsonPath;
        }

        @Override
        public void parseValue(ObjectNode valueNode, JsonNode resultNode) {
            // 1. 解析目标路径
            NestedPathResult target = resolveNestedPath(valueNode, fieldName);
            String targetFieldName = target.targetFieldName();
            ObjectNode parentNode = target.parentNode();
            JsonNode existingValue = parentNode.get(targetFieldName);

            // 2. 获取源数据
            JsonNode sourceValues = ValueParseUtils.getSourceValueFromJsonPath(resultNode, jsonPath);

            // 3. 特殊处理字符串类型
            if (Objects.nonNull(fieldType) && AbilityFieldType.STRING.equals(fieldType)) {
                ArrayNode tempArray = JsonNodeFactory.instance.arrayNode();
                // 添加源数据
                addToArrayNode(tempArray, sourceValues);
                // 添加现有数据
                addToArrayNode(tempArray, existingValue);
                // 将数组转为字符串
                parentNode.set(targetFieldName, JsonNodeFactory.instance.textNode(tempArray.toString()));
            } else {
                // 4. 处理数组类型
                ArrayNode targetArray;
                if (existingValue == null || existingValue.isNull()) {
                    targetArray = parentNode.putArray(targetFieldName);
                } else if (existingValue.isArray()) {
                    targetArray = (ArrayNode) existingValue;
                } else {
                    throw new IllegalArgumentException("目标字段不是数组: " + fieldName + ", 当前值: " + existingValue);
                }
                // 添加源数据到目标数组
                addToArrayNode(targetArray, sourceValues);
            }
        }

        /**
         * 将源节点数据添加到数组节点
         *
         * @param arrayNode Array 节点
         * @param source    源节点
         */
        private void addToArrayNode(ArrayNode arrayNode, JsonNode source) {
            if (source == null || source.isNull() || source.isMissingNode()) {
                return;
            }
            if (source.isArray()) {
                source.forEach(arrayNode::add);
            } else {
                arrayNode.add(source);
            }
        }
    }

    /**
     * 根路径绑定，直接把返回结果覆盖到消息上
     */
    @Getter
    public static class RootBinding extends Binding {

        public RootBinding() {
            this.method = ResultBindMethod.ROOT;
            this.fieldName = ROOT_FIELD;
        }

        @Override
        public String getJsonPath() {
            return null;
        }

        @Override
        public void parseValue(ObjectNode valueNode, JsonNode resultNode) {
            // ignore
        }
    }

    /**
     * 嵌套路径解析结果
     */
    private static final class NestedPathResult {

        private final ObjectNode parentNode;
        private final String targetFieldName;

        public NestedPathResult(ObjectNode parentNode, String targetFieldName) {
            this.parentNode = parentNode;
            this.targetFieldName = targetFieldName;
        }

        public ObjectNode parentNode() {
            return parentNode;
        }

        public String targetFieldName() {
            return targetFieldName;
        }
    }


    /**
     * 解析嵌套字段路径，自动创建中间节点
     *
     * @param rootNode  根节点（从此节点开始解析）
     * @param fieldPath 嵌套路径（如 "user.address.city"）
     * @return 解析结果，包含父节点和最终字段名
     */
    private static NestedPathResult resolveNestedPath(ObjectNode rootNode, String fieldPath) {
        // 按点号分割路径
        String[] pathSegments = fieldPath.split("\\.");
        ObjectNode currentNode = rootNode;

        // 逐层处理路径（排除最后一个字段名，因为它需要由调用方设置）
        for (int i = 0; i < pathSegments.length - 1; i++) {
            String segment = pathSegments[i];
            JsonNode childNode = currentNode.get(segment);

            // 如果路径不存在或不是对象，创建空对象节点
            if (childNode == null || !childNode.isObject()) {
                childNode = JsonUtils.emptyNode();
                currentNode.set(segment, childNode);
            }
            // 继续深入下一层
            currentNode = (ObjectNode) childNode;
        }

        return new NestedPathResult(currentNode, pathSegments[pathSegments.length - 1]);
    }

    /**
     * 根据指定的字段类型转换值
     *
     * @param sourceValue 源值
     * @param fieldType   字段类型
     * @param itemType    元素类型
     * @return 转换后的JsonNode
     */
    private static JsonNode convertValueToType(JsonNode sourceValue, AbilityFieldType fieldType,
        AbilityFieldType itemType) {
        if (sourceValue == null || sourceValue.isNull() || sourceValue.isMissingNode()) {
            return sourceValue;
        }

        JsonNodeFactory factory = JsonNodeFactory.instance;
        String strValue = sourceValue.isTextual() ? sourceValue.asText() : sourceValue.toString();

        try {
            switch (fieldType) {
                case STRING:
                case DATE:
                case DATETIME:
                case TEXT:
                case CHAR:
                    return factory.textNode(strValue);
                case INT:
                case SHORT:
                    return factory.numberNode(Integer.parseInt(strValue.trim()));
                case LONG:
                    return factory.numberNode(Long.parseLong(strValue.trim()));
                case FLOAT:
                    return factory.numberNode(Float.parseFloat(strValue.trim()));
                case DOUBLE:
                    return factory.numberNode(Double.parseDouble(strValue.trim()));
                case BOOLEAN:
                    return convertToBoolean(strValue, factory);
                case ARRAY:
                    return processArrayType(sourceValue, factory, itemType);
                case OBJECT:
                    return processObjectType(sourceValue, strValue);
                case INT_VECTOR:
                case FLOAT_VECTOR:
                case BYTE_VECTOR:
                    return processVectorType(sourceValue, strValue);
                case VOID:
                    return factory.nullNode();
                default:
                    throw new IllegalArgumentException("不支持的字段类型: " + fieldType);
            }
        } catch (Exception e) {
            if (AbilityFieldType.ARRAY.equals(fieldType)) {
                throw new IllegalArgumentException(
                    String.format("无法将值 %s 转换为指定类型 %s", sourceValue, fieldType + "[" + itemType + "]"), e);
            } else {
                throw new IllegalArgumentException(
                    String.format("无法将值 %s 转换为指定类型 %s", sourceValue, fieldType), e);
            }
        }
    }

    /**
     * 将字符串值转换为布尔节点
     *
     * @param strValue str 值
     * @param factory  Json节点工厂
     * @return {@link JsonNode }
     */
    private static JsonNode convertToBoolean(String strValue, JsonNodeFactory factory) {
        String lowered = strValue.toLowerCase().trim();
        boolean boolVal =
            "true".equals(lowered) || "1".equals(lowered) || "yes".equals(lowered) || "是".equals(lowered);
        return factory.booleanNode(boolVal);
    }

    /**
     * 处理数组类型的转换
     *
     * @param sourceValue 源值
     * @param factory     json节点
     * @param itemType    类型
     * @return {@link JsonNode }
     */
    private static JsonNode processArrayType(JsonNode sourceValue, JsonNodeFactory factory, AbilityFieldType itemType) {
        if (sourceValue.isArray()) {
            ArrayNode resultArray = factory.arrayNode();
            for (JsonNode element : sourceValue) {
                // 如果设置了itemType，则根据itemType转换数组元素
                if (itemType != null) {
                    resultArray.add(convertValueToType(element, itemType, null));
                } else {
                    resultArray.add(element);
                }
            }
            return resultArray;
        } else {
            ArrayNode arrayNode = factory.arrayNode();
            arrayNode.add(convertValueToType(sourceValue, itemType, null));
            return arrayNode;
        }
    }

    /**
     * 处理对象类型的转换
     *
     * @param sourceValue 源值
     * @param strValue    str 值
     * @return {@link JsonNode }
     */
    private static JsonNode processObjectType(JsonNode sourceValue, String strValue) {
        if (sourceValue.isObject()) {
            return sourceValue;
        } else {
            // 尝试解析为JSON对象
            return JsonUtils.parseJsonNode(strValue);
        }
    }

    /**
     * 处理向量类型的转换
     *
     * @param sourceValue 源值
     * @param strValue    str 值
     * @return {@link JsonNode }
     */
    private static JsonNode processVectorType(JsonNode sourceValue, String strValue) {
        // 向量类型保持原样，假设它们已经是正确的格式
        if (sourceValue.isArray()) {
            return sourceValue;
        } else {
            return JsonUtils.parseObject(strValue);
        }
    }


    /**
     * 从 JSON 节点创建 OutputBind 对象
     *
     * @param json JSON 节点
     * @return OutputBind 对象
     */
    public static OutputBind fromJson(JsonNode json) {
        Map<String, Binding> orderedBindings = new LinkedHashMap<>();
        List<Entry<String, Binding>> tempList = new ArrayList<>();

        // 遍历 JSON 节点
        JsonNode bindingsNode = json.has("bindings") ? json.get("bindings") : json;
        bindingsNode.fields().forEachRemaining(field -> {
            JsonNode bindingNode = field.getValue();
            String fieldName = bindingNode.has("fieldName") ? bindingNode.get("fieldName").asText() : field.getKey();
            Binding binding = parseOutputBinding(bindingNode, fieldName);
            tempList.add(new SimpleEntry<>(fieldName, binding));
        });

        tempList.sort(Comparator.comparingInt(e -> e.getValue().getOrder()));
        tempList.forEach(entry -> orderedBindings.put(entry.getKey(), entry.getValue()));
        OutputBind outputBind = new OutputBind();
        outputBind.bindings.clear();
        outputBind.bindings.putAll(orderedBindings);
        return outputBind;
    }

    private static Binding parseOutputBinding(JsonNode bindingNode, String fieldName) {
        ResultBindMethod method = JsonUtils.treeToValue(bindingNode.get("method"), ResultBindMethod.class);
        String typeName = bindingNode.has("fieldType") ? bindingNode.get("fieldType").asText() : "STRING";

        AbilityFieldType type = getAbilityFieldType(typeName);
        AbilityFieldType itemType = null;
        if (Objects.isNull(method)) {
            throw new IllegalArgumentException("Unknown binding type for parameter: " + fieldName);
        }
        if (AbilityFieldType.ARRAY.equals(type)) {
            String itemTypeName = bindingNode.has("itemType") ? bindingNode.get("itemType").asText() : "STRING";
            itemType = getAbilityFieldType(itemTypeName);
        }
        AbilityFieldObjectType objectType = null;
        if (AbilityFieldType.OBJECT.equals(type)) {
            objectType = bindingNode.has("objectType") ? JsonUtils.treeToValue(bindingNode.get("objectType"),
                AbilityFieldObjectType.class) : null;
        }
        String fieldZhName = bindingNode.has("fieldZhName") ? bindingNode.get("fieldZhName").asText() : fieldName;
        String jsonPath = bindingNode.has(JSON_PATH) ? bindingNode.get(JSON_PATH).asText() : "";
        Integer order = bindingNode.has("order") ? bindingNode.get("order").asInt() : 0;
        // 根据绑定类型动态创建 Binding
        switch (method) {
            case REPLACE:
                return new ReplaceBinding(jsonPath, fieldName, type, order, itemType, objectType);
            case APPEND:
                return new AppendBinding(jsonPath, fieldName, fieldZhName, type, order, itemType, objectType);
            case CONCAT:
                return new ConcatBinding(jsonPath, fieldName, type, order, itemType, objectType);
            case ROOT:
                return new RootBinding();
            default:
                throw new IllegalArgumentException("Unknown binding type for parameter: " + fieldName);
        }
    }

    private static AbilityFieldType getAbilityFieldType(String typeName) {
        AbilityFieldType type;
        try {
            type = AbilityFieldType.valueOf(typeName.toUpperCase());
        } catch (IllegalArgumentException e) {
            FieldType fieldType = FieldType.getByName(typeName);
            type = AbilityFieldType.fromFieldType(fieldType);
        }
        return type;
    }

    /**
     * 从输出参数绑定关系中获取参数字段英文名和参数值
     *
     * @param result      输出消息内容
     * @param inputFields 算子输入参数
     * @return 参数
     */
    public Map<String, Object> getResultMap(JsonNode result, OperatorRowType inputFields) {
        Map<String, Object> map = new HashMap<>(bindings.size());
        for (Entry<String, Binding> entry : this.bindings.entrySet()) {
            Binding binding = entry.getValue();
            if (binding instanceof RootBinding) {
                return JsonUtils.jsonNodeToMap(result);
            } else {
                map.put(createFieldName(binding.getFieldName(), inputFields), result.get(binding.getFieldName()));
            }
        }
        return map;
    }

    /**
     * 从算子输入参数中获取参数字段中文名
     *
     * @param fieldName   参数字段英文名
     * @param inputFields 算子输入参数
     * @return 参数字段中文名
     */
    private String createFieldName(String fieldName, OperatorRowType inputFields) {
        for (Entry<String, Schema> entry : inputFields.entrySet()) {
            if (fieldName.equals(entry.getKey())) {
                return entry.getValue().getZhName() + "(" + entry.getKey() + ")";
            }
        }
        return fieldName;
    }
}
