<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.moye.base.data.model.dao.DataModelFieldMapper">
    <!-- 结果集映射 -->
    <resultMap id="BaseResultMap" type="com.trs.moye.base.data.model.entity.DataModelField">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="data_model_id" jdbcType="BIGINT" property="dataModelId"/>
        <result column="zh_name" jdbcType="VARCHAR" property="zhName"/>
        <result column="en_name" jdbcType="VARCHAR" property="enName"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="type_name" jdbcType="VARCHAR" property="typeName"/>
        <result column="is_use_standard" jdbcType="BIT" property="useStandard"/>
        <result column="is_multi_value" jdbcType="BIT" property="multiValue"/>
        <result column="is_statistic" jdbcType="BIT" property="statistic"/>
        <result column="is_nullable" jdbcType="BIT" property="nullable"/>
        <result column="is_built_in" jdbcType="BIT" property="builtIn"/>
        <result column="is_primary_key" jdbcType="BIT" property="primaryKey"/>
        <result column="is_increment" jdbcType="BIT" property="increment"/>
        <result column="is_partition" jdbcType="BIT" property="partition"/>
        <result column="is_title" jdbcType="BIT" property="title"/>
        <result column="is_foreign_key" jdbcType="BIT" property="foreignKey"/>
        <result column="foreign_key_config" property="foreignKeyConfig"
            typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler"/>
        <result column="fields" property="fields"
            typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="description" jdbcType="LONGVARCHAR" property="description"/>
        <result column="advance_config" property="advanceConfig"
            typeHandler="com.trs.moye.base.common.typehandler.PolymorphismTypeHandler"/>
    </resultMap>
    <delete id="deleteByDataModelId">
        delete from data_model_field
        <where>
            and data_model_id = #{dataModelId,jdbcType=BIGINT}
        </where>
    </delete>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select *
        from data_model_field
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectByIdCollection" resultMap="BaseResultMap">
        select
        *
        from data_model_field
        <where>
            <choose>
                <when test="idCollection == null or idCollection.size == 0">
                    and false
                </when>
                <otherwise>
                    and id in
                    <foreach close=")" collection="idCollection" item="id" open="(" separator=",">
                        #{id,jdbcType=BIGINT}
                    </foreach>
                </otherwise>
            </choose>
        </where>
    </select>
    <select id="getByZhName" resultMap="BaseResultMap">
        select
        *
        from data_model_field
        <where>
            and zh_name = #{zhName,jdbcType=VARCHAR}
        </where>
    </select>
    <select id="getByEnName" resultMap="BaseResultMap">
        select
        *
        from data_model_field
        <where>
            and en_name = #{enName,jdbcType=VARCHAR}
        </where>
    </select>
    <select id="listByDataModelId" resultMap="BaseResultMap">
        select
        *
        from data_model_field
        <where>
            and data_model_id = #{dataModelId,jdbcType=BIGINT}
        </where>
    </select>
    <select id="listByZhNameCollection" resultMap="BaseResultMap">
        select
        *
        from data_model_field
        <where>
            <choose>
                <when test="zhNameCollection == null or zhNameCollection.size == 0">
                    and false
                </when>
                <otherwise>
                    and zh_name in
                    <foreach close=")" collection="zhNameCollection" item="zhName" open="(" separator=",">
                        #{zhName,jdbcType=VARCHAR}
                    </foreach>
                </otherwise>
            </choose>
        </where>
    </select>
    <select id="listByEnNameCollection" resultMap="BaseResultMap">
        select
        *
        from data_model_field
        <where>
            <if test="dataModelId != null">
                and data_model_id = #{dataModelId,jdbcType=BIGINT}
            </if>
            <choose>
                <when test="enNameCollection == null or enNameCollection.size == 0">
                    and false
                </when>
                <otherwise>
                    and en_name in
                    <foreach close=")" collection="enNameCollection" item="enName" open="(" separator=",">
                        #{enName,jdbcType=VARCHAR}
                    </foreach>
                </otherwise>
            </choose>
        </where>
    </select>
    <select id="listByDataModelIdCollection" resultMap="BaseResultMap">
        select
        *
        from data_model_field
        <where>
            <choose>
                <when test="dataModelIdCollection == null or dataModelIdCollection.size == 0">
                    and false
                </when>
                <otherwise>
                    and data_model_id in
                    <foreach close=")" collection="dataModelIdCollection" item="dataModelId" open="(" separator=",">
                        #{dataModelId,jdbcType=BIGINT}
                    </foreach>
                </otherwise>
            </choose>
        </where>
    </select>
    <select id="listQuery" resultMap="BaseResultMap">
        select
        *
        from data_model_field
        <where>
            <if test="zhName != null and zhName != ''">
                and zh_name = #{zhName,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <!-- 以上代码由MbgCode自动生成2024/9/26 下午5:15 -->


    <!-- 以下为您的代码 -->
    <select id="listStandardFieldByEnName" resultMap="BaseResultMap">
        select *
        from data_model_field
        where en_name = #{enName}
        and `is_use_standard` = 1
    </select>
    <select id="selectByDataModelId" resultMap="BaseResultMap">
        select *
        from data_model_field
        where data_model_id = #{dataModelId}
    </select>
    <select id="selectDefaultAuditFields" resultType="java.lang.Integer">
        select id
        from data_model_field
        where is_built_in = true
    </select>

    <select id="selectByIds" resultMap="BaseResultMap">
        select * from data_model_field where
        <choose>
            <when test="ids.isEmpty == false">
                id in
                <foreach collection="ids" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </when>
            <otherwise>
                1=-1
            </otherwise>
        </choose>
    </select>
    <select id="getIncrementField" resultMap="BaseResultMap">
        select *
        from data_model_field
        where data_model_id = #{dataModelId,jdbcType=BIGINT}
        and `is_increment` = 1 limit 1
    </select>

    <select id="getPrimaryKey" resultMap="BaseResultMap">
        select *
        from data_model_field
        where data_model_id = #{dataModelId,jdbcType=BIGINT}
        and `is_primary_key` = 1 limit 1
    </select>

    <select id="countStandardFieldUsage" resultType="java.lang.Integer">
        select count(distinct data_model_id)
        from data_model_field
        where en_name = #{standardFieldEnName}
        and `is_use_standard` = 1
    </select>

    <update id="updateEntityTypeName">
        UPDATE `data_model_field`
        SET `type_name` = #{newTypeName}
        where `type` = #{type}
        and `type_name` = #{oldTypeName}
    </update>
    <update id="updateDefaultValue">
        update `data_model_field`
        <set>
            `default_value` = #{field.defaultValue}
        </set>
        where id = #{field.id}
    </update>

    <select id="selectByTypeTypeName" resultMap="BaseResultMap">
        select *
        from data_model_field
        where `type` = #{type}
        and type_name = #{typeName}
    </select>

    <delete id="deleteByEnNameAndTypeAndDataModelIds">
        delete from data_model_field
        <where>
            <if test="dataModelIds!= null and dataModelIds.size > 0">
                and data_model_id in
                <foreach collection="dataModelIds" item="dataModelId" separator="," open="(" close=")">
                    #{dataModelId}
                </foreach>
            </if>
            <foreach collection="type2EnNames" index="type" item="enNames" separator=" OR " open=" AND (" close=")">
                (
                `type` = #{type}
                AND en_name in
                <foreach collection="enNames" item="enName" separator=", " open="(" close=")">
                    #{enName}
                </foreach>
                )
            </foreach>
        </where>
    </delete>

    <update id="updateFieldsName">
        UPDATE data_model_field
        SET en_name = CASE
        <foreach collection="fields" index="index" item="field">
            WHEN en_name = #{field.oldName} AND `type` = #{field.type} THEN #{field.newName}
        </foreach>
        ELSE en_name
        END
        WHERE data_model_id = #{dataModelId}
    </update>
    <select id="selectByStorageIds" resultMap="BaseResultMap">
        select * from data_model_field
        <where>
            <choose>
                <when test="storageIds == null or storageIds.size == 0">
                    false
                </when>
                <otherwise>
                    data_model_id in (
                    select data_model_id from data_storage where id in
                    <foreach collection="storageIds" item="storageId" separator="," open="(" close=")">
                        #{storageId}
                    </foreach>
                    )
                </otherwise>
            </choose>
        </where>
    </select>
    <select id="selectCreateTableFields" resultMap="BaseResultMap">
        SELECT * from data_model_field WHERE id in (
        SELECT df.field_id
        FROM data_storage ds, JSON_TABLE(
        ds.field_ids,
        '$[*]' COLUMNS(field_id INT PATH '$')
        ) AS df
        WHERE ds.data_model_id = 10619
        );
    </select>

    <select id="selectTitleFieldNameByDataModelId" resultType="java.lang.String">
        select en_name from data_model_field
        where data_model_id = #{dataModelId}
          and is_title = 1
    </select>
</mapper>