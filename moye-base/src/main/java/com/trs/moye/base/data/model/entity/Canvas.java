package com.trs.moye.base.data.model.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 画布
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/8/5 17:26
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Canvas {

    private Integer width;

    private Integer height;

    private Integer top;

    private Integer left;

    private String color;

    /**
     * 获取默认画布
     *
     * @return canvas
     */
    public static Canvas getDefaultCanvas() {
        return new Canvas(1200, 1000, 0, 0, null);
    }
}
