package com.trs.moye.ability.entity.operator;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.trs.moye.ability.deserializer.InputBindDeserializer;
import com.trs.moye.ability.deserializer.OperatorRowTypeDeserializer;
import com.trs.moye.ability.deserializer.OutputBindDeserializer;
import com.trs.moye.ability.entity.Ability;
import com.trs.moye.ability.entity.ArrangeOperatorConfig;
import com.trs.moye.ability.entity.InputBind;
import com.trs.moye.ability.entity.OutputBind;
import com.trs.moye.ability.enums.ArrangeNodeType;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.data.connection.entity.KerberosCertificate;
import com.trs.moye.base.data.connection.entity.params.ConnectionParams;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.data.model.dao.DataModelFieldMapper;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.base.data.model.enums.CreateTableStatus;
import com.trs.moye.base.data.model.enums.DataSaveMode;
import com.trs.moye.base.data.service.entity.Condition;
import com.trs.moye.base.data.storage.DataStorage;
import com.trs.moye.base.data.storage.dao.DataStorageMapper;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

/**
 * BatchOperator 批处理算子运行时实体 包含所有运行批处理算子所需信息，例如数据库连接信息，算子能力信息等 batch-engine 将此算子信息记录到 redis 上, spark-application 从 redis
 * 上取
 *
 * <AUTHOR>
 * @since 2025/3/27 17:09
 */
@Data
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
public class BatchOperatorRuntimeEntity {

    /**
     * 算子id
     */
    private Long displayId;

    /**
     * 父节点算子id
     */
    private List<Long> parentDisplayIds;

    /**
     * 输出表名称
     */
    private String outputTableName;

    /**
     * 输入的算子id和表名
     */
    private Map<Long, String> inputTables;

    /**
     * 算子名称
     */
    private String name;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 条件
     */
    private List<Condition> conditions;

    /**
     * 数据接入时执行sql（指标库任务用）
     */
    private String conditionSql;

    /**
     * 数据来源/数据存储/能力
     */
    private Type type;

    /**
     * 输入字段
     */
    @JsonDeserialize(using = OperatorRowTypeDeserializer.class)
    private OperatorRowType inputFields;

    /**
     * 输出字段
     */
    @JsonDeserialize(using = OperatorRowTypeDeserializer.class)
    private OperatorRowType outputFields;

    /**
     * 输入绑定
     */
    @JsonDeserialize(using = InputBindDeserializer.class)
    private InputBind inputBind;

    /**
     * 输出绑定
     */
    @JsonDeserialize(using = OutputBindDeserializer.class)
    private OutputBind outputBind;

    /**
     * 能力
     */
    private Ability ability;

    /**
     * 来源表数据连接信息, 包含来源表 表名，主键，增量字段名
     */
    private TableInfo sourceTableInfo;

    /**
     * 存储表数据连接信息, 包含存储表 表名，主键，增量字段名
     */
    private List<TableInfo> storageTableInfos;

    /**
     * 算子为数据来源/数据存储时的表信息 1. 数据库连接信息(注意密码是加密的, 在spark运行时解密使用) 2. 表信息 3. 主键, 用于存储es时指定文档id的字段 4. 增量字段名, 用于数据来源增量读取数据
     */
    @Data
    public static class TableInfo {

        /**
         * 表名
         */
        private String dbTable;
        /**
         * 连接类型
         */
        private ConnectionType connectionType;
        /**
         * 连接参数
         */
        private ConnectionParams connectionParams;
        /**
         * kerberos参数
         */
        private KerberosCertificate kerberosCertificate;
        /**
         * doris 连接方式, 默认为jdbc 可选值: jdbc, connector
         */
        private String dorisAccessMode;
        /**
         * 增量字段名
         */
        private String incrementField;
        /**
         * 主键
         */
        private String primaryKey;
        /**
         * 子查询筛选条件
         */
        private String subquery;
        /**
         * 删除分区, 暂时只实现clickhouse的
         */
        private List<String> deletePartitions;

        /**
         * 数据存储模式 追加、覆盖、替换
         */
        private DataSaveMode saveMode;

        /**
         * 从dataStorage创建TableInfo
         *
         * @param dataStorage      DataStorage
         * @param deletePartitions 删除分区
         * @return TableInfo
         */
        public static TableInfo fromDataStorage(DataStorage dataStorage, List<String> deletePartitions) {
            TableInfo tableInfo = new TableInfo();
            tableInfo.dbTable = dataStorage.getEnName();
            tableInfo.connectionType = dataStorage.getConnection().getConnectionType();
            tableInfo.connectionParams = dataStorage.getConnection().getConnectionParams();
            tableInfo.kerberosCertificate = dataStorage.getConnection().getKerberosCertificate();
            tableInfo.deletePartitions = deletePartitions;
            tableInfo.saveMode = dataStorage.getSaveMode();
            return tableInfo;
        }

        /**
         * 从dataStorage创建TableInfo
         *
         * @param dataStorage DataStorage
         * @return TableInfo
         */
        public static TableInfo fromDataStorage(DataStorage dataStorage) {
            return fromDataStorage(dataStorage, null);
        }

        /**
         * 构建数据源表信息
         *
         * @param dataStorage    贴源表存储点
         * @param incrementField 增量字段名
         * @return {@link TableInfo }
         */
        public static TableInfo buildSourceTableInfo(DataStorage dataStorage, String incrementField) {
            TableInfo tableInfo = fromDataStorage(dataStorage);
            tableInfo.incrementField = incrementField;
            tableInfo.dorisAccessMode = System.getenv("DORIS_ACCESS_MODE");
            log.info("dorisAccessMode:{}", tableInfo.dorisAccessMode);
            return tableInfo;
        }

        /**
         * 构建存储表信息
         *
         * @param dataStorage 当前表存储点
         * @param primaryKey  主键
         * @return {@link TableInfo }
         */
        public static TableInfo buildStorageTableInfo(DataStorage dataStorage, String primaryKey) {
            TableInfo tableInfo = fromDataStorage(dataStorage);
            tableInfo.primaryKey = primaryKey;
            tableInfo.dorisAccessMode = System.getenv("DORIS_ACCESS_MODE");
            log.info("dorisAccessMode:{}", tableInfo.dorisAccessMode);
            return tableInfo;
        }
    }

    /**
     * 算子实际类型: 数据来源/数据存储/能力
     */
    public enum Type {
        SOURCE,
        STORAGE,
        ABILITY;

        /**
         * TABLE类型算子并且是当前数据建模，则为数据存储
         *
         * @param arrangeNodeType       算子类型
         * @param dataModelId           数据建模id
         * @param dataModelIdInOperator 算子中的数据建模id
         * @return 算子实际类型
         */
        static Type from(ArrangeNodeType arrangeNodeType, Integer dataModelId, Integer dataModelIdInOperator) {
            if (arrangeNodeType.isTable()) {
                if (dataModelId.equals(dataModelIdInOperator)) {
                    return STORAGE;
                } else {
                    return SOURCE;
                }
            }
            return ABILITY;
        }
    }


    /**
     * 将算子列表转化为可执行算子列表
     *
     * @param operators            算子列表
     * @param dataModelId          数据建模id
     * @param dataModelFieldMapper dataModelFieldMapper
     * @param dataStorageMapper    dataStorageMapper
     * @param dataModelMapper      dataModelMapper
     * @return 可执行算子列表
     */
    @JsonIgnore
    public static List<BatchOperatorRuntimeEntity> buildRuntimeEntities(List<BatchOperator> operators,
        Integer dataModelId, DataModelFieldMapper dataModelFieldMapper, DataStorageMapper dataStorageMapper,
        DataModelMapper dataModelMapper) {
        // 算子基础信息
        List<BatchOperatorRuntimeEntity> runtimeEntities = operators.stream().map(
            operator -> BatchOperatorRuntimeEntity.from(operator, dataModelFieldMapper, dataStorageMapper,
                dataModelMapper)).collect(Collectors.toList());

        // 算子 InputTables
        Map<Long, String> displayId2OutputTableName = operators.stream()
            .collect(Collectors.toMap(BatchOperator::getDisplayId, BatchOperator::getOutputTableName));
        runtimeEntities.forEach(runtimeEntity -> runtimeEntity.setInputTables(
            runtimeEntity.getParentDisplayIds().stream()
                .collect(Collectors.toMap(displayId -> displayId, displayId2OutputTableName::get))));

        // 算子顺序
        return sortOperators(dataModelId, runtimeEntities);
    }

    @JsonIgnore
    private static BatchOperatorRuntimeEntity from(BatchOperator batchOperator,
        DataModelFieldMapper dataModelFieldMapper, DataStorageMapper dataStorageMapper,
        DataModelMapper dataModelMapper) {
        BatchOperatorRuntimeEntity runtimeEntity = new BatchOperatorRuntimeEntity();
        runtimeEntity.setDisplayId(batchOperator.getDisplayId());
        runtimeEntity.setParentDisplayIds(batchOperator.getParentDisplayIds());
        runtimeEntity.setOutputTableName(batchOperator.getOutputTableName());
        runtimeEntity.setName(batchOperator.getName());
        runtimeEntity.setEnabled(batchOperator.getEnabled());
        runtimeEntity.setConditions(batchOperator.getConditions());
        // 转化type
        runtimeEntity.setType(
            Type.from(batchOperator.getType(), batchOperator.getDataModelId(), batchOperator.getTableDataModelId()));
        runtimeEntity.setInputFields(batchOperator.getInputFields());
        runtimeEntity.setOutputFields(batchOperator.getOutputFields());
        runtimeEntity.setInputBind(batchOperator.getInputBind());
        runtimeEntity.setOutputBind(batchOperator.getOutputBind());
        runtimeEntity.setAbility(batchOperator.getAbility());
        // type为 SOURCE/STORAGE, 设置表信息
        if (runtimeEntity.getType().equals(Type.SOURCE)) {
            runtimeEntity.setSourceTableInfo(
                buildSourceTableInfo(batchOperator, dataModelFieldMapper, dataStorageMapper));
        } else if (runtimeEntity.getType().equals(Type.STORAGE)) {
            runtimeEntity.setStorageTableInfos(
                buildStorageTableInfo(batchOperator, dataModelFieldMapper, dataModelMapper));
        }
        return runtimeEntity;
    }

    @NotNull
    @JsonIgnore
    private static TableInfo buildSourceTableInfo(BatchOperator batchOperator,
        DataModelFieldMapper dataModelFieldMapper,
        DataStorageMapper dataStorageMapper) {
        // 查询 data storage
        DataStorage storage = dataStorageMapper.selectByIdWithConnection(batchOperator.getTableStorageId());
        String incrementField = null;
        if (Boolean.TRUE.equals(batchOperator.getTableIsIncrement())) {
            // 查询 增量字段
            incrementField = Optional.ofNullable(
                    dataModelFieldMapper.getIncrementField(batchOperator.getTableDataModelId()))
                .map(DataModelField::getEnName).orElse(null);
        }
        return TableInfo.buildSourceTableInfo(storage, incrementField);
    }

    @JsonIgnore
    private static List<TableInfo> buildStorageTableInfo(BatchOperator batchOperator,
        DataModelFieldMapper dataModelFieldMapper, DataModelMapper dataModelMapper) {
        DataModel dataModel = dataModelMapper.selectById(batchOperator.getTableDataModelId());
        String primaryKey = Optional.ofNullable(dataModelFieldMapper.getPrimaryKey(dataModel.getId()))
            .map(DataModelField::getEnName).orElse(null);
        return dataModel.getDataStorages().stream()
            .filter(storage -> CreateTableStatus.SUCCESS.equals(storage.getCreateTableStatus()))
            .map(storage -> TableInfo.buildStorageTableInfo(storage, primaryKey)).collect(Collectors.toList());
    }

    private static List<BatchOperatorRuntimeEntity> sortOperators(Integer dataModelId,
        List<BatchOperatorRuntimeEntity> runtimeEntities) {
        // 结果列表
        List<BatchOperatorRuntimeEntity> sorted = new ArrayList<>();
        // 记录已排序的算子id
        Set<Long> completedDisplayIds = new HashSet<>(runtimeEntities.size());
        // 等待排序的算子
        List<BatchOperatorRuntimeEntity> pending = new ArrayList<>(runtimeEntities);
        while (!pending.isEmpty()) {
            // 找到pending列表中所有父节点已在completed列表的算子
            List<BatchOperatorRuntimeEntity> ready = pending.stream()
                .filter(operator -> completedDisplayIds.containsAll(operator.getParentDisplayIds()))
                .collect(Collectors.toList());
            if (ready.isEmpty()) {
                throw new BizException("算子排序编译失败，DAG连线异常！数据建模id:" + dataModelId);
            }
            for (BatchOperatorRuntimeEntity operator : ready) {
                sorted.add(operator);
                completedDisplayIds.add(operator.getDisplayId());
                pending.remove(operator);
            }
        }
        return sorted;
    }


    /**
     * to 流处理算子，用于执行流处理能力
     *
     * @return 流处理算子
     */
    public Operator toOperator() {
        Operator operator = new Operator();
        operator.setConditions(this.conditions.toArray(new Condition[0]));
        operator.setName(this.name);
        operator.setInputFields(this.inputFields);
        operator.setOutputFields(this.outputFields);
        operator.setAbility(this.ability);
        operator.setInputBind(this.inputBind);
        operator.setOutputBind(this.outputBind);
        operator.setConfig(new ArrangeOperatorConfig());
        return operator;
    }
}
