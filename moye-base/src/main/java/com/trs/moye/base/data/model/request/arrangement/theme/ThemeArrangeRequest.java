package com.trs.moye.base.data.model.request.arrangement.theme;

import com.trs.moye.base.common.annotaion.validation.NotRepeat;
import com.trs.moye.base.common.utils.AssertUtils;
import com.trs.moye.base.data.model.entity.Canvas;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025-07-31 14:54
 */
@Data
public class ThemeArrangeRequest {

    /**
     * 画布信息
     */
    private Canvas canvas;

    /**
     * 来源表信息
     */
    @NotEmpty(message = "来源表信息不能为空")
    @NotRepeat(message = "来源表不能重复", fields = {"dataModelId:来源表id", "storageId:来源存储id"})
    @Valid
    private List<ArrangeSourceTable> sourceTables;

    /**
     * 聚合表
     */
    @NotNull(message = "聚合表不能为空")
    private ArrangeAggregationTable aggregationTable;

    /**
     * 联表关系
     */
    private List<JoinRelation> joinRelations;

    /**
     * 解析来源表模型ID集合
     *
     * @return 解析来源表模型ID集合
     */
    public Set<Integer> parseSourceModelIds() {
        Set<Integer> sourceModelIds = sourceTables.stream().map(ArrangeSourceTable::getDataModelId)
            .collect(Collectors.toSet());
        AssertUtils.equals(sourceTables.size(), sourceModelIds.size(), "来源表存在重复表");
        return sourceModelIds;
    }

    /**
     * 检查并排序关联关系，确保不存在循环依赖和游离的关联关系
     */
    public void checkSortJoinRelation() {
        LinkedList<JoinRelation> result = new LinkedList<>();
        JoinRelation joinRelation0 = joinRelations.get(0);
        Set<JoinRelation> tempJoinRelationSet = new HashSet<>(joinRelations);
        tempJoinRelationSet.remove(joinRelation0);
        result.addLast(joinRelation0);
        // 添加右边的关联关系
        addRightJoinRelation(joinRelation0, result, tempJoinRelationSet);
        // 添加左边的关联关系
        addLeftJoinRelation(joinRelation0, result, tempJoinRelationSet);
        if (!tempJoinRelationSet.isEmpty()) {
            throw new IllegalArgumentException("存在游离的关联关系");
        }
        joinRelations = new ArrayList<>(result);
    }

    private void addRightJoinRelation(JoinRelation joinRelation0, LinkedList<JoinRelation> result,
        Set<JoinRelation> tempJoinRelationSet) {
        // 添加后续的关联关系
        JoinRelation temp = joinRelation0;
        while (temp != null) {
            JoinRelation relation = getJoinRelationBySourceStorageId(temp.getTargetStorageId());
            if (relation != null) {
                result.addLast(relation);
                tempJoinRelationSet.remove(relation);
            }
            if (relation == joinRelation0) {
                throw new IllegalArgumentException("存在循环关联关系");
            }
            temp = relation;
        }
    }

    private void addLeftJoinRelation(JoinRelation joinRelation0, LinkedList<JoinRelation> result,
        Set<JoinRelation> tempJoinRelationSet) {
        // 添加后续的关联关系
        JoinRelation temp = joinRelation0;
        // 添加前置的关联关系
        while (temp != null) {
            JoinRelation relation = getJoinRelationByTargetStorageId(temp.getSourceStorageId());
            if (relation != null) {
                result.addFirst(relation);
                tempJoinRelationSet.remove(relation);
            }
            if (relation == joinRelation0) {
                throw new IllegalArgumentException("存在循环关联关系");
            }
            temp = relation;
        }
    }

    private JoinRelation getJoinRelationBySourceStorageId(Integer sourceStorageId) {
        for (JoinRelation joinRelation : joinRelations) {
            if (joinRelation.getSourceStorageId().equals(sourceStorageId)) {
                return joinRelation;
            }
        }
        return null;
    }

    private JoinRelation getJoinRelationByTargetStorageId(Integer targetStorageId) {
        for (JoinRelation joinRelation : joinRelations) {
            if (joinRelation.getTargetStorageId().equals(targetStorageId)) {
                return joinRelation;
            }
        }
        return null;
    }
}
