package com.trs.ai.moye.data.model.request;

import com.trs.ai.moye.data.model.dto.StoragePointParams;
import com.trs.ai.moye.data.model.entity.DataModelBasicInfo;
import com.trs.ai.moye.data.model.enums.ArrangeDisplayType;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.data.execute.ExecuteModeEnum;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.enums.CreateModeEnum;
import com.trs.moye.base.data.model.enums.ModelExecuteStatus;
import com.trs.moye.base.data.schedule.ScheduleInfo;
import java.util.List;
import java.util.Objects;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

/**
 * 创建要素库
 *
 * <AUTHOR>
 * @since 2024/5/15 16:15
 */
@Data
@Validated
public class DwdAddRequest {

    /**
     * 业务分类id
     */
    @NotNull(message = "业务分类id不能为空")
    private Integer businessCategoryId;

    /**
     * 基础信息
     */
    @NotNull(message = "基本信息不能为空")
    private DataModelBasicInfo basicInfo;

    /**
     * 选择的数据来源源ID，这个地方是其他建模ID
     */
    @NotEmpty
    private List<Integer> dataSourceIds;


    /**
     * 选择的存储ID
     */
    @NotEmpty
    private List<StoragePointParams> storagePointList;

//    /**
//     * 调度配置
//     */
//    @NotNull(message = "调度信息不能为空")
//    private ScheduleInfo scheduleInfo;

//
//    /**
//     * 算子编排显示类型
//     */
//    private ArrangeDisplayType processingMode;


    /**
     * 分层
     */
    private ModelLayer modelLayer;
//
//
//    /**
//     * 元数据标准 id 通过 元数据标准 创建的元数据 会有 元数据标准id
//     */
//    private Integer metaDataStandardId;


    /**
     * 数据源信息
     *
     * <AUTHOR>
     * @since 2024/9/29 14:49
     */
    @Data
    public static class SourceInfos {

        private Boolean arrangeStatus;
        private Integer categoryId;
        private String dataSourceCategory;
        private String dataSourceType;
        private Integer dispatchType;
        private String enName;
        private Integer id;
        private Boolean publishStatus;
        private Integer tableType;
        private String zhName;

    }

//    /**
//     * 获取执行方式（流/批）
//     *
//     * @return {@link ExecuteModeEnum}
//     */
//    @JsonIgnore
//    public ExecuteModeEnum getExecuteMode() {
//        return getScheduleInfo().getExecuteMode();
//    }


    /**
     * 要素库建模构建实体
     *
     * @return {@link DataModel}
     * <AUTHOR>
     * @since 2024/9/29 14:46
     */
    public DataModel toModel() {
        DataModel dataModel = new DataModel();
        //基本信息
        dataModel.setZhName(basicInfo.getZhName());
        dataModel.setEnName(basicInfo.getEnName());
        dataModel.setDescription(basicInfo.getDescription());
        //业务分类id
        dataModel.setBusinessCategoryId(businessCategoryId);
        // 创建模式默认元数据建模，如果有元数据标准id则是元数据标准
        dataModel.setCreateMode(CreateModeEnum.NORMAL);
//        if (Objects.nonNull(metaDataStandardId)) {
//            dataModel.setCreateMode(CreateModeEnum.META_DATA_STANDARD);
//            dataModel.setMetaDataStandardId(metaDataStandardId);
//        }
        //调度状态初始化为关闭
        dataModel.setExecuteStatus(ModelExecuteStatus.STOP);
        //分层--要素库
        dataModel.setLayer(modelLayer);
        //是否治理默认为否
        dataModel.setIsArranged(false);
        return dataModel;
    }
}
