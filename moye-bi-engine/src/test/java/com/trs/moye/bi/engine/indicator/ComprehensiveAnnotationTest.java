package com.trs.moye.bi.engine.indicator;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.trs.moye.bi.engine.indicator.entity.CalculationFunctionDefinition;
import com.trs.moye.bi.engine.indicator.entity.CalculationParameter;
import com.trs.moye.bi.engine.indicator.function.CalculationFunctionRegistry;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

/**
 * 综合注解验证测试类
 * <p>
 * 验证所有计算函数的注解添加情况，确保：
 * <ol>
 *   <li>所有 CalculationFunction 枚举值对应的方法都有注解</li>
 *   <li>所有注解都符合规范要求</li>
 *   <li>所有参数都有正确的描述</li>
 *   <li>复杂参数映射能够正确处理</li>
 * </ol>
 * </p>
 *
 * <AUTHOR>
 * @since 2025/07/29
 */
@DisplayName("综合注解验证测试")
class ComprehensiveAnnotationTest {

    /**
     * 所有需要验证的函数列表（基于 CalculationFunction 枚举）
     */
    private static final List<String> ALL_FUNCTIONS = Arrays.asList(
        // ArithmeticOperations
        "add", "subtract", "multiply", "divide",

        // ComparativeAnalysis
        "getFieldData", "getPreviousPeriodData", "getPeriodOverPeriodData",
        "getYearOverYearData", "getYearAgoData", "getYoYByFields",

        // DataAggregation
        "getRatioData", "getRankData", "horizontalSum", "verticalSum",
        "verticalSumEx", "cumulativeSum", "dailyAverage",

        // ConditionalCalculations
        "isConditionMet", "countConditionMet", "getConditionalData",

        // TrendAnalysis
        "getTrendData", "getConsecutiveTrendData",

        // ExternalDataFetch
        "getDataBySql",

        // SequenceGeneration
        "generateSequence"
    );

    /**
     * 测试前的初始化
     * <p>
     * 确保函数注册表在综合验证测试执行前已正确初始化。
     * </p>
     */
    @BeforeEach
    void setUp() {
        // 确保函数注册表已初始化
        CalculationFunctionRegistry.ensureInitialized();
    }

    /**
     * 验证所有函数的注解完整性
     */
    @Test
    @DisplayName("验证所有函数的注解完整性")
    void verifyAllFunctionAnnotations() {
        int totalFunctions = ALL_FUNCTIONS.size();
        int annotatedFunctions = 0;
        int missingFunctions = 0;

        for (String functionName : ALL_FUNCTIONS) {
            Optional<CalculationFunctionDefinition> definitionOpt =
                CalculationFunctionRegistry.getDefinition(functionName);

            if (definitionOpt.isPresent()) {
                CalculationFunctionDefinition definition = definitionOpt.get();
                assertTrue(verifyFunctionDefinition(definition),
                    "函数定义应该完整: " + functionName);
                annotatedFunctions++;
            } else {
                missingFunctions++;
            }
        }

        // 验证完成率
        assertEquals(0, missingFunctions, "不应该有缺少注解的函数");
        assertEquals(totalFunctions, annotatedFunctions, "所有函数都应该有注解");

        double completionRate = (double) annotatedFunctions / totalFunctions * 100;
        assertEquals(100.0, completionRate, 0.1, "完成率应该是100%");
    }

    /**
     * 验证函数分类统计
     */
    @Test
    @DisplayName("验证函数分类统计")
    void verifyFunctionCategories() {
        Map<String, Object> stats = CalculationFunctionRegistry.getStatistics();
        assertNotNull(stats, "统计信息不应为空");
        assertTrue(stats.containsKey("totalFunctions"), "应该包含总函数数量");

        Object totalFunctions = stats.get("totalFunctions");
        assertNotNull(totalFunctions, "总函数数量不应为空");
        assertInstanceOf(Integer.class, totalFunctions, "总函数数量应该是整数");
        assertTrue((Integer) totalFunctions >= ALL_FUNCTIONS.size(),
            "总函数数量应该大于等于预期函数数量");

        @SuppressWarnings("unchecked")
        Map<String, Long> categoryStats = (Map<String, Long>) stats.get("categoryStats");
        if (categoryStats != null) {
            assertFalse(categoryStats.isEmpty(), "分类统计不应为空");
        }

        List<String> categories = CalculationFunctionRegistry.getAllCategories();
        assertNotNull(categories, "分类列表不应为空");
        assertFalse(categories.isEmpty(), "应该有至少一个分类");
    }

    /**
     * 验证复杂参数映射函数
     * <p>
     * 专门验证具有复杂参数映射逻辑的函数，这些函数通常具有可变参数列表、 智能类型推断或重载方法合并等特殊处理逻辑。
     * </p>
     *
     * @param functionName 要验证的复杂函数名称，包括： - verticalSum: 可变维度字段列表 - verticalSumEx: 布尔值智能判断 - dailyAverage: 类型推断 -
     *                     getTrendData: 重载方法合并
     */
    @ParameterizedTest
    @DisplayName("验证复杂参数映射函数")
    @ValueSource(strings = {"verticalSum", "verticalSumEx", "dailyAverage", "getTrendData"})
    void verifyComplexParameterFunctions(String functionName) {
        Optional<CalculationFunctionDefinition> definitionOpt =
            CalculationFunctionRegistry.getDefinition(functionName);

        assertTrue(definitionOpt.isPresent(), "复杂函数定义应该存在: " + functionName);

        CalculationFunctionDefinition definition = definitionOpt.get();

        // 验证基本属性
        assertEquals(functionName, definition.getFunctionName());
        assertNotNull(definition.getDescription());
        assertFalse(definition.getDescription().trim().isEmpty());

        // 验证参数数量合理
        assertTrue(definition.getTotalParameterCount() > 0, "应该有参数");
        assertTrue(definition.getRequiredParameterCount() > 0, "应该有必需参数");

        // 验证参数定义
        List<CalculationParameter> parameters = definition.getParameters();
        for (CalculationParameter param : parameters) {
            assertNotNull(param.getName(), "参数名称不应为空");
            assertNotNull(param.getDescription(), "参数描述不应为空");
            assertNotNull(param.getType(), "参数类型不应为空");
            assertTrue(param.getLegacyIndex() >= 0, "legacyIndex 应该大于等于0");
        }
    }

    /**
     * 验证重载函数处理
     * <p>
     * 验证具有多个重载版本的函数是否正确合并为统一的函数定义， 包括参数数量统计、必需/可选参数识别和参数顺序验证。
     * </p>
     *
     * @param functionName 要验证的重载函数名称，包括： - getTrendData: 1/4/5参数版本 - dailyAverage: 1/2/3参数版本 - verticalSumEx: 3/4参数版本
     */
    @ParameterizedTest
    @DisplayName("验证重载函数处理")
    @ValueSource(strings = {"getTrendData", "dailyAverage", "verticalSumEx"})
    void verifyOverloadedFunctions(String functionName) {
        Optional<CalculationFunctionDefinition> definitionOpt =
            CalculationFunctionRegistry.getDefinition(functionName);

        assertTrue(definitionOpt.isPresent(), "重载函数定义应该存在: " + functionName);

        CalculationFunctionDefinition definition = definitionOpt.get();

        // 重载函数应该有合理的参数结构
        int totalParams = definition.getTotalParameterCount();
        int requiredParams = definition.getRequiredParameterCount();
        int optionalParams = definition.getOptionalParameterCount();

        assertEquals(totalParams, requiredParams + optionalParams,
            "总参数数量应该等于必需参数加可选参数");

        // 重载函数通常有可选参数
        if ("getTrendData".equals(functionName) || "dailyAverage".equals(functionName)) {
            assertTrue(optionalParams > 0, functionName + " 应该有可选参数");
        }
    }

    /**
     * 验证算术运算函数
     * <p>
     * 验证基础算术运算函数的注解完整性，这些函数通常具有相似的参数结构， 主要接受字段列表参数进行数学运算。
     * </p>
     *
     * @param functionName 要验证的算术函数名称，包括基本的四则运算函数
     */
    @ParameterizedTest
    @DisplayName("验证算术运算函数")
    @ValueSource(strings = {"add", "subtract", "multiply", "divide"})
    void verifyArithmeticFunctions(String functionName) {
        Optional<CalculationFunctionDefinition> definitionOpt =
            CalculationFunctionRegistry.getDefinition(functionName);

        assertTrue(definitionOpt.isPresent(), "算术函数定义应该存在: " + functionName);

        CalculationFunctionDefinition definition = definitionOpt.get();

        // 算术函数应该有字段列表参数
        List<CalculationParameter> parameters = definition.getParameters();
        assertFalse(parameters.isEmpty(), "算术函数应该有参数");

        // 第一个参数通常是字段列表
        CalculationParameter firstParam = parameters.get(0);
        assertTrue(firstParam.isRequired(), "第一个参数应该是必需的");
        assertEquals(0, firstParam.getLegacyIndex(), "第一个参数的 legacyIndex 应该是 0");
    }

    /**
     * 验证比较分析函数
     * <p>
     * 验证用于数据比较分析的函数，这些函数通常用于计算同比、环比、 历史数据对比等分析指标，第一个参数通常是目标字段。
     * </p>
     *
     * @param functionName 要验证的比较分析函数名称，包括各种时间维度的数据对比函数
     */
    @ParameterizedTest
    @DisplayName("验证比较分析函数")
    @ValueSource(strings = {"getFieldData", "getPreviousPeriodData", "getPeriodOverPeriodData",
        "getYearOverYearData", "getYearAgoData", "getYoYByFields"})
    void verifyComparativeFunctions(String functionName) {
        Optional<CalculationFunctionDefinition> definitionOpt =
            CalculationFunctionRegistry.getDefinition(functionName);

        assertTrue(definitionOpt.isPresent(), "比较分析函数定义应该存在: " + functionName);

        CalculationFunctionDefinition definition = definitionOpt.get();

        // 比较分析函数应该有目标字段参数
        List<CalculationParameter> parameters = definition.getParameters();
        assertFalse(parameters.isEmpty(), "比较分析函数应该有参数");

        // 通常第一个参数是目标字段
        CalculationParameter firstParam = parameters.get(0);
        assertTrue(firstParam.isRequired(), "第一个参数应该是必需的");

        // 参数名称通常包含 "field" 或 "targetField"
        String paramName = firstParam.getName().toLowerCase();
        assertTrue(paramName.contains("field") || paramName.contains("target"),
            "第一个参数名称应该与字段相关: " + firstParam.getName());
    }

    /**
     * 验证单个函数定义的完整性
     *
     * @param definition 定义
     * @return boolean
     */
    private boolean verifyFunctionDefinition(CalculationFunctionDefinition definition) {
        // 验证基本信息
        if (definition.getDescription() == null || definition.getDescription().trim().isEmpty()) {
            return false;
        }

        if (definition.getCategory() == null || definition.getCategory().trim().isEmpty()) {
            return false;
        }

        // 验证参数信息
        List<CalculationParameter> parameters = definition.getParameters();
        if (parameters != null) {
            for (CalculationParameter param : parameters) {
                if (param.getName() == null || param.getName().trim().isEmpty()) {
                    return false;
                }
                if (param.getDescription() == null || param.getDescription().trim().isEmpty()) {
                    return false;
                }
                if (param.getType() == null) {
                    return false;
                }
                if (param.getLegacyIndex() < 0) {
                    return false;
                }
            }
        }

        // 验证执行器
        return definition.getExecutor() != null;
    }
}
