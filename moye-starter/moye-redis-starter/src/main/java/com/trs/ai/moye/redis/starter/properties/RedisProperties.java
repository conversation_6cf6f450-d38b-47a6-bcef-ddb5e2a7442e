package com.trs.ai.moye.redis.starter.properties;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Redis配置属性类
 *
 * <AUTHOR>
 * @since 2025/6/20 11:02
 */
@Data
@Component
@Slf4j
@ConfigurationProperties(prefix = "moye.redis")
public class RedisProperties {

    /**
     * 默认为svc地址
     */
    private String host;
    /**
     * Redis端口
     */
    private Integer port;
    /**
     * 外部调用ip
     */
    private String externalHost;
    /**
     * 外部调用端口
     */
    private Integer externalPort;

    private String password;

    private Database database = new Database();

    /**
     * redis缓存默认过期时间（min）
     */
    private Integer cacheTtlMinutes = 30;

    /**
     * Redis数据库配置类
     */
    @Data
    public static class Database {

        private Integer backend = 0;

        private Integer batchEngine = 1;

        private Integer streamEngine = 2;

        private Integer storageEngine = 3;

        private Integer mcpService = 4;
    }


    /**
     * 根据应用名称获取对应database
     *
     * @param appName spring.application.name
     * @return database
     */
    public Integer getDatabaseByAppName(String appName) {
        switch (appName) {
            // backend和stream-engine使用同一个数据库，以防止修改backend算子编排无法同时修改到stream-engine的redis
            case "moye":
            case "moye-stream-engine":
                return database.getBackend();
            case "moye-batch-engine":
                return database.getBatchEngine();
            case "moye-storage-engine":
                return database.getStorageEngine();
            case "moye-mcp-service":
                return database.getMcpService();
            default:
                throw new IllegalArgumentException("Unknown app name: " + appName);
        }
    }

    /**
     * 获取外部调用的host，如果未设置则使用默认的host
     *
     * @return 外部调用的host
     */
    public String getExternalHost() {
        if (externalHost != null && !externalHost.isEmpty()) {
            return externalHost;
        } else {
            log.warn("External host is not set, using default host: {}", host);
            return host; // 如果没有设置外部调用IP，则使用默认的host
        }
    }

    /**
     * 获取外部调用的端口，如果未设置则使用默认的port
     *
     * @return 外部调用的端口
     */
    public int getExternalPort() {
        if (externalPort != null) {
            return externalPort;
        } else {
            log.warn("External port is not set, using default port: {}", port);
            return port; // 如果没有设置外部调用端口，则使用默认的port
        }
    }
}
