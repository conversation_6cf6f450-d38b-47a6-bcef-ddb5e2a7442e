package com.trs.ai.moye.data.model.response;

import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.data.model.entity.DataModel;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025-07-31 11:37
 */
@Data
@NoArgsConstructor
public class ThemeModelSourceResponse {

    /**
     * 建模id
     */
    private Integer id;
    /**
     * 英文名
     */
    private String enName;
    /**
     * 中文名
     */
    private String zhName;

    /**
     * 表所属分层
     */
    private ModelLayer modelLayer;

    /**
     * 是否治理
     */
    private Boolean isArranged;

    /**
     * 业务分类id
     */
    private Integer categoryId;

    /**
     * 业务分类名称
     */
    private String categoryName;

    public ThemeModelSourceResponse(DataModel model){
        this.id = model.getId();
        this.enName = model.getEnName();
        this.zhName = model.getZhName();
        this.modelLayer = model.getLayer();
        this.isArranged = model.getIsArranged();
        this.categoryId = model.getBusinessCategoryId();
    }
}
