package com.trs.ai.moye.minio.starter.config;

import com.trs.ai.moye.minio.starter.properties.MinioProperties;
import io.minio.MinioClient;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * minio配置类
 *
 * <AUTHOR>
 */
@Configuration
@EnableConfigurationProperties(MinioProperties.class)
public class MinioConfiguration {

    /**
     * minio 客户端配置
     *
     * @param minioProperties minio配置
     * @return minio客户端
     */
    @Bean(name = "minioClient")
    MinioClient minioClient(MinioProperties minioProperties) {
        return MinioClient.builder()
            .endpoint(minioProperties.getEndpoint())
            .credentials(minioProperties.getAccessKey(), minioProperties.getSecretKey())
            .build();
    }

}
