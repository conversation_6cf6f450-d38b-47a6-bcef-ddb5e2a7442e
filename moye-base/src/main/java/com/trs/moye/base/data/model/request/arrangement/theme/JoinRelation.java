package com.trs.moye.base.data.model.request.arrangement.theme;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025-07-31 15:23
 */
@Data
@NoArgsConstructor
public class JoinRelation {

    /**
     * 来源存储id
     */
    private Integer sourceStorageId;

    /**
     * 来源字段英文名
     */
    @NotBlank
    private String sourceField;

    /**
     * 目标存储id
     */
    private Integer targetStorageId;

    /**
     * 连接表join字段英文名
     */
    @NotBlank
    private String targetField;

    /**
     * join类型，合法值参考【spark.ability.batch.Join】的joinTypeSet静态属性
     */
    @NotNull
    private String joinType;
}
