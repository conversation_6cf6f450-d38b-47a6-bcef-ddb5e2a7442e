<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.moye.base.data.source.dao.DataSourceConfigMapper">
    <resultMap id="DataSourceConfigMap" type="com.trs.moye.base.data.model.entity.DataSourceConfig">
        <result column="id" property="id"/>
        <result column="en_name" property="enName"/>
        <result column="zh_name" property="zhName"/>
        <result column="connection_id" property="connectionId"/>
        <result column="data_model_id" property="dataModelId"/>
        <result column="source_model_id" property="sourceModelId"/>
        <result column="settings" property="settings"
            typeHandler="com.trs.moye.base.common.typehandler.PolymorphismTypeHandler"/>
        <association column="connection_id" property="connection"
            javaType="com.trs.moye.base.data.connection.entity.DataConnection"
            select="com.trs.moye.base.data.connection.dao.DataConnectionMapper.selectById"/>
    </resultMap>

    <select id="selectByConnectionId" resultMap="DataSourceConfigMap">
        SELECT *
        FROM data_source_config
        WHERE connection_id = #{connectionId};
    </select>

    <select id="selectById" resultMap="DataSourceConfigMap">
        select *
        from data_source_config
        where id = #{id}
    </select>
    <select id="selectOneByDataModelId" resultMap="DataSourceConfigMap">
        select *
        from data_source_config
        where data_model_id = #{dataModelId}
        limit 1
    </select>
    <select id="selectByDataModelIds" resultType="com.trs.moye.base.data.model.entity.DataSourceConfig">
        SELECT * FROM data_source_config
        WHERE data_model_id IN
        <foreach collection="sourceModelIds" item="dataModelId" open="(" separator="," close=")">
            #{dataModelId}
        </foreach>
    </select>

    <select id="selectByDataModel" resultMap="DataSourceConfigMap">
        select *
        from data_source_config
        where data_model_id = #{dataModelId}
    </select>


    <delete id="deleteByDataModelId">
        delete
        from data_source_config
        where data_model_id = #{dataModelId}
    </delete>

    <select id="selectBySourceModelId" resultMap="DataSourceConfigMap">
        select *
        from data_source_config
        where source_model_id = #{sourceModelId}
    </select>

    <update id="updateBatch">
        update data_source_config
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="zh_name = case" suffix="end,">
                <foreach collection="dataSourceConfigs" item="config">
                    <if test="config.zhName != null">
                        when id = #{config.id} then #{config.zhName}
                    </if>
                </foreach>
            </trim>
            <trim prefix="en_name = case" suffix="end,">
                <foreach collection="dataSourceConfigs" item="config">
                    <if test="config.enName != null">
                        when id = #{config.id} then #{config.enName}
                    </if>
                </foreach>
            </trim>
            <trim prefix="connection_id = case" suffix="end,">
                <foreach collection="dataSourceConfigs" item="config">
                    <if test="config.connectionId != null">
                        when id = #{config.id} then #{config.connectionId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="settings = case" suffix="end,">
                <foreach collection="dataSourceConfigs" item="config">
                    <if test="config.settings != null">
                        when id = #{config.id} then #{config.settings,typeHandler=com.trs.moye.base.common.typehandler.PolymorphismTypeHandler}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="dataSourceConfigs" item="config" open="(" separator="," close=")">
            #{config.id}
        </foreach>
    </update>
</mapper>