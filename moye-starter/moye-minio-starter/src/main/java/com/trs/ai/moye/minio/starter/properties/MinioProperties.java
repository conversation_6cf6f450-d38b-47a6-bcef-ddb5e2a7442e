package com.trs.ai.moye.minio.starter.properties;

import javax.validation.constraints.NotBlank;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

/**
 * minio配置属性类
 *
 * <AUTHOR>
 * @since 2024/10/29 14:14
 */
@Data
@Validated
@ConfigurationProperties(prefix = "com.trs.minio")
public class MinioProperties {

    /**
     * 连接地址（默认为svc地址）
     */
    @NotBlank(message = "endpoint 不能为空字符串")
    private String endpoint;
    /**
     * 外部访问地址
     */
    private String externalEndpoint;
    /**
     * 用户名
     */
    @NotBlank(message = "accessKey 不能为空字符串")
    private String accessKey;
    /**
     * 密码
     */
    @NotBlank(message = "secretKey 不能为空字符串")
    private String secretKey;
    /**
     * buckets
     */
    private Bucket bucket = new Bucket();

    /**
     * buckets
     */
    @Data
    public static class Bucket {

        /**
         * 批处理任务详情日志 bucket
         */
        @NotBlank(message = "logs 不能为空字符串")
        private String logs;

        /**
         * 文件建模 bucket
         */
        private String fileModeling = "moye-data-model-file";

        /**
         * MCP文件 bucket
         */
        private String mcp = "moye-mcp-file";
    }


    /**
     * 获取外部访问地址，如果未设置则返回默认的 endpoint
     *
     * @return 外部访问地址
     */
    public String getExternalEndpoint() {
        if (externalEndpoint == null || externalEndpoint.isEmpty()) {
            return endpoint;
        }
        return externalEndpoint;
    }
}