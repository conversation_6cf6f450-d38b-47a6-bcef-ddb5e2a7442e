spring.application.name=moye-storage-engine
# Nacos配置中心
spring.cloud.nacos.username=nacos
spring.cloud.nacos.password=nacos
spring.cloud.nacos.server-addr=192.168.211.100:8849
spring.cloud.nacos.config.namespace=local-zal
spring.cloud.nacos.discovery.namespace=${spring.cloud.nacos.config.namespace}
spring.config.import[0]=nacos:moye-storage-engine.properties?refresh=true
spring.config.import[1]=nacos:xxl-job.properties?refresh=true
spring.config.import[2]=nacos:kafka.properties?refresh=true
spring.config.import[3]=nacos:mysql-moye-v4.properties?refresh=true
spring.config.import[4]=nacos:clickhouse.properties?refresh=true
spring.config.import[5]=nacos:kafka-log-server.properties
spring.config.import[6]=nacos:nlp-service.properties
logging.level.com.alibaba.cloud.nacos=debug