package com.trs.moye.bi.engine.indicator;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.trs.moye.base.data.indicator.entity.IndicatorApplicationFieldConfig;
import com.trs.moye.bi.engine.indicator.context.ParameterContext;
import com.trs.moye.bi.engine.indicator.entity.CalculationFunctionDefinition;
import com.trs.moye.bi.engine.indicator.exception.IndicatorQueryException;
import com.trs.moye.bi.engine.indicator.function.CalculationFunctionRegistry;
import com.trs.moye.bi.engine.indicator.function.ParameterResolver;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

/**
 * 复杂传统参数映射兼容性测试类
 * <p>
 * 专门验证和测试复杂传统 List&lt;String&gt; 参数配置的兼容性处理，确保特殊的参数映射场景在新系统中能够正确工作。 重点测试以下复杂参数映射场景：
 * <ol>
 *   <li>verticalSum: [targetField, ...dimensionFields, newFieldName, newFieldValue]</li>
 *   <li>verticalSumEx: 根据最后一个参数是否为布尔值选择重载版本</li>
 *   <li>dailyAverage: 根据参数数量和类型智能选择重载方法</li>
 * </ol>
 * </p>
 *
 * <AUTHOR>
 * @since 2025/07/29
 */
@DisplayName("复杂参数兼容性测试")
class ComplexParameterCompatibilityTest {

    private CalculationFunctionDefinition verticalSumDefinition;
    private CalculationFunctionDefinition verticalSumExDefinition;
    private CalculationFunctionDefinition dailyAverageDefinition;

    /**
     * 测试前的初始化
     * <p>
     * 确保函数注册表已初始化，并获取测试所需的复杂参数映射函数定义。
     * </p>
     */
    @BeforeEach
    void setUp() {
        // 确保函数注册表已初始化
        CalculationFunctionRegistry.ensureInitialized();

        Optional<CalculationFunctionDefinition> verticalSumOpt =
            CalculationFunctionRegistry.getDefinition("verticalSum");
        assertTrue(verticalSumOpt.isPresent(), "verticalSum 函数定义应该存在");
        verticalSumDefinition = verticalSumOpt.get();

        Optional<CalculationFunctionDefinition> verticalSumExOpt =
            CalculationFunctionRegistry.getDefinition("verticalSumEx");
        assertTrue(verticalSumExOpt.isPresent(), "verticalSumEx 函数定义应该存在");
        verticalSumExDefinition = verticalSumExOpt.get();

        Optional<CalculationFunctionDefinition> dailyAverageOpt =
            CalculationFunctionRegistry.getDefinition("dailyAverage");
        assertTrue(dailyAverageOpt.isPresent(), "dailyAverage 函数定义应该存在");
        dailyAverageDefinition = dailyAverageOpt.get();
    }

    /**
     * 测试 verticalSum 复杂参数映射 - 正常情况
     * <p>
     * 验证 verticalSum 函数在正常参数配置下的复杂参数映射功能， 包括可变长度维度字段列表的正确解析和参数分配。
     * </p>
     *
     * @param paramString 逗号分隔的参数字符串，格式为 "targetField,dim1,dim2,...,newFieldName,newFieldValue"
     * @param description 测试用例的描述信息，用于标识不同的测试场景
     */
    @ParameterizedTest
    @DisplayName("测试 verticalSum 复杂参数映射 - 正常情况")
    @CsvSource({
        "'sales,region,total,合计', 多维度字段",
        "'sales,region,product,category,total,合计', 多维度字段",
        "'sales,total,合计', 无维度字段"
    })
    void testVerticalSumParameterMappingNormal(String paramString, String description) {
        List<String> params = Arrays.asList(paramString.split(","));

        IndicatorApplicationFieldConfig config = new IndicatorApplicationFieldConfig();
        config.setFunction("verticalSum");
        config.setInput(params);

        ParameterContext context = ParameterResolver.resolve(verticalSumDefinition, config);

        assertNotNull(context, "参数上下文不应为空");

        // 验证参数解析结果
        String targetField = context.getString("targetField");

        String newFieldName = context.getString("newFieldName");
        String newFieldValue = context.getString("newFieldValue");

        // 验证解析正确性
        assertEquals(params.get(0), targetField, "targetField 应该正确解析");
        assertEquals(params.get(params.size() - 2), newFieldName, "newFieldName 应该正确解析");
        assertEquals(params.get(params.size() - 1), newFieldValue, "newFieldValue 应该正确解析");
        List<String> dimensionFields = (List<String>) context.getObject("dimensionFields");
        assertNotNull(dimensionFields, "dimensionFields 不应为空");

        // 验证维度字段数量
        int expectedDimensionCount = params.size() - 3; // 总数减去 targetField, newFieldName, newFieldValue
        assertEquals(expectedDimensionCount, dimensionFields.size(),
            "维度字段数量应该正确: " + description);
    }

    /**
     * 测试 verticalSum 参数不足的边界情况
     */
    @Test
    @DisplayName("测试 verticalSum 参数不足的边界情况")
    void testVerticalSumParameterInsufficient() {
        List<String> params = List.of("sales"); // 只有1个参数，不足

        IndicatorApplicationFieldConfig config = new IndicatorApplicationFieldConfig();
        config.setFunction("verticalSum");
        config.setInput(params);

        // 应该抛出异常
        assertThrows(IndicatorQueryException.class, () -> {
            ParameterResolver.resolve(verticalSumDefinition, config);
        }, "参数不足时应该抛出异常");
    }

    /**
     * 测试 verticalSumEx 复杂参数映射 - 布尔值版本
     * <p>
     * 验证 verticalSumEx 函数在最后一个参数为布尔值时的智能重载选择功能， 确保系统能够正确识别四参数版本并解析布尔参数。
     * </p>
     *
     * @param paramString 逗号分隔的参数字符串，格式为 "targetField,dim1,dim2,...,jsonConfig,booleanValue"
     * @param description 测试用例的描述信息，用于标识不同的布尔值测试场景
     */
    @ParameterizedTest
    @DisplayName("测试 verticalSumEx 复杂参数映射 - 布尔值版本")
    @CsvSource({
        "'sales,region,product,{\"total\":\"合计\"},true', 四参数布尔版本(true)",
        "'sales,region,product,{\"total\":\"合计\"},false', 四参数布尔版本(false)"
    })
    void testVerticalSumExParameterMappingBoolean(String paramString, String description) {
        List<String> params = Arrays.asList(paramString.split(","));

        IndicatorApplicationFieldConfig config = new IndicatorApplicationFieldConfig();
        config.setFunction("verticalSumEx");
        config.setInput(params);

        ParameterContext context = ParameterResolver.resolve(verticalSumExDefinition, config);

        assertNotNull(context, "参数上下文不应为空");

        // 验证参数解析结果
        String targetField = context.getString("targetField");
        @SuppressWarnings("unchecked")
        List<String> dimensionFields = (List<String>) context.getObject("dimensionFields");
        String newFieldsAndValuesJson = context.getString("newFieldsAndValues");  // 修复：使用正确的参数名称

        assertEquals(params.get(0), targetField, "targetField 应该正确解析");
        assertNotNull(dimensionFields, "dimensionFields 不应为空");
        assertTrue(newFieldsAndValuesJson.contains("total"), "JSON 配置应该包含预期内容");
        Boolean summarizeSingleRowGroups = (Boolean) context.getObject("summarizeSingleRowGroups");
        assertNotNull(summarizeSingleRowGroups, "布尔参数应该被正确解析: " + description);

        // 验证布尔值
        String lastParam = params.get(params.size() - 1);
        assertEquals(Boolean.valueOf(lastParam), summarizeSingleRowGroups,
            "布尔值应该正确解析");
    }

    /**
     * 测试 verticalSumEx 复杂参数映射 - 非布尔值版本
     */
    @Test
    @DisplayName("测试 verticalSumEx 复杂参数映射 - 非布尔值版本")
    void testVerticalSumExParameterMappingNonBoolean() {
        List<String> params = Arrays.asList("sales", "region", "product", "{\"total\":\"合计\"}");

        IndicatorApplicationFieldConfig config = new IndicatorApplicationFieldConfig();
        config.setFunction("verticalSumEx");
        config.setInput(params);

        ParameterContext context = ParameterResolver.resolve(verticalSumExDefinition, config);

        assertNotNull(context, "参数上下文不应为空");

        // 验证参数解析结果
        String targetField = context.getString("targetField");
        @SuppressWarnings("unchecked")
        List<String> dimensionFields = (List<String>) context.getObject("dimensionFields");

        assertEquals("sales", targetField);
        assertNotNull(dimensionFields);
        assertEquals(2, dimensionFields.size(), "应该有2个维度字段");
        String newFieldsAndValuesJson = context.getString("newFieldsAndValues");
        assertTrue(newFieldsAndValuesJson.contains("total"));
        // 三参数版本，布尔参数应该为null或有默认值
        // 这里不强制要求为null，因为可能有默认值
    }

    /**
     * 测试 dailyAverage 复杂参数映射 - 类型推断
     * <p>
     * 验证 dailyAverage 函数的智能类型推断功能，确保系统能够根据参数数量和类型 正确选择对应的重载方法，特别是区分精度参数（整数）和周期类型参数（字符串）。
     * </p>
     *
     * @param paramString 逗号分隔的参数字符串，可能的格式包括： - "targetField" (一参数版本) - "targetField,precision" (二参数精度版本) -
     *                    "targetField,periodType" (二参数周期版本) - "targetField,periodType,precision" (三参数完整版本)
     * @param description 测试用例的描述信息，用于标识不同的参数组合场景
     */
    @ParameterizedTest
    @DisplayName("测试 dailyAverage 复杂参数映射 - 类型推断")
    @CsvSource({
        "'sales', 一参数版本",
        "'sales,2', 二参数精度版本",
        "'sales,MONTH', 二参数周期版本",
        "'sales,MONTH,2', 三参数完整版本"
    })
    void testDailyAverageParameterMapping(String paramString, String description) {
        List<String> params = Arrays.asList(paramString.split(","));

        IndicatorApplicationFieldConfig config = new IndicatorApplicationFieldConfig();
        config.setFunction("dailyAverage");
        config.setInput(params);

        ParameterContext context = ParameterResolver.resolve(dailyAverageDefinition, config);

        assertNotNull(context, "参数上下文不应为空");

        // 验证参数解析结果
        String targetField = context.getString("targetField");
        String periodType = context.getString("periodType");
        Integer precision = (Integer) context.getObject("precision");

        assertEquals(params.get(0), targetField, "targetField 应该正确解析");

        // 根据参数数量验证类型推断
        if (params.size() == 2) {
            String secondParam = params.get(1);
            try {
                Integer.valueOf(secondParam);
                // 应该被识别为精度参数
                assertNotNull(precision, "精度参数应该被正确识别: " + description);
            } catch (NumberFormatException e) {
                // 应该被识别为周期类型参数
                assertNotNull(periodType, "周期类型参数应该被正确识别: " + description);
            }
        } else if (params.size() == 3) {
            assertNotNull(periodType, "三参数版本应该有周期类型");
            assertNotNull(precision, "三参数版本应该有精度");
        }
    }

    /**
     * 测试 dailyAverage 边界情况 - 参数为空
     */
    @Test
    @DisplayName("测试 dailyAverage 边界情况 - 参数为空")
    void testDailyAverageParameterEmpty() {
        List<String> params = List.of(); // 空参数列表

        IndicatorApplicationFieldConfig config = new IndicatorApplicationFieldConfig();
        config.setFunction("dailyAverage");
        config.setInput(params);

        // 应该抛出异常
        assertThrows(IndicatorQueryException.class, () -> {
            ParameterResolver.resolve(dailyAverageDefinition, config);
        }, "空参数时应该抛出异常");
    }

    /**
     * 测试 dailyAverage 边界情况 - 无效精度
     */
    @Test
    @DisplayName("测试 dailyAverage 边界情况 - 无效精度")
    void testDailyAverageParameterInvalidPrecision() {
        List<String> params = Arrays.asList("sales", "MONTH", "abc"); // 第三个参数不是有效整数

        IndicatorApplicationFieldConfig config = new IndicatorApplicationFieldConfig();
        config.setFunction("dailyAverage");
        config.setInput(params);

        // 应该抛出异常
        assertThrows(IndicatorQueryException.class, () -> {
            ParameterResolver.resolve(dailyAverageDefinition, config);
        }, "无效精度时应该抛出异常");
    }

    /**
     * 测试复杂参数映射的性能
     */
    @Test
    @DisplayName("测试复杂参数映射的性能")
    void testComplexParameterMappingPerformance() {
        List<String> params = Arrays.asList("sales", "region", "product", "total", "合计");

        IndicatorApplicationFieldConfig config = new IndicatorApplicationFieldConfig();
        config.setFunction("verticalSum");
        config.setInput(params);

        // 执行多次解析，测试性能
        long startTime = System.currentTimeMillis();
        int iterations = 100;

        for (int i = 0; i < iterations; i++) {
            ParameterContext context = ParameterResolver.resolve(verticalSumDefinition, config);
            assertNotNull(context, "每次解析都应该成功");
        }

        long duration = System.currentTimeMillis() - startTime;

        // 验证性能（平均每次解析应该在合理时间内完成）
        double avgTime = (double) duration / iterations;
        assertTrue(avgTime < 10.0, "平均解析时间应该小于10ms，实际: " + avgTime + "ms");
    }
}
