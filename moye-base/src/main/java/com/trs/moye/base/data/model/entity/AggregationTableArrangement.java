package com.trs.moye.base.data.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.moye.base.common.entity.AuditBaseEntity;
import com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler;
import com.trs.moye.base.data.model.request.arrangement.theme.ArrangeAggregationTable;
import com.trs.moye.base.data.model.request.arrangement.theme.ArrangeSourceTable;
import com.trs.moye.base.data.model.request.arrangement.theme.JoinRelation;
import com.trs.moye.base.data.model.request.arrangement.theme.ThemeArrangeRequest;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 聚合表编排信息
 *
 * <AUTHOR>
 * @since 2025-07-31 17:40
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@TableName(value = "aggregation_table_arrangement", autoResultMap = true)
public class AggregationTableArrangement extends AuditBaseEntity {

    /**
     * 数据模型ID
     */
    private Integer dataModelId;

    /**
     * 画布信息
     */
    @TableField(typeHandler = CustomJacksonTypeHandler.class)
    private Canvas canvas;

    /**
     * 来源表信息
     */
    @TableField(typeHandler = CustomJacksonTypeHandler.class)
    private List<ArrangeSourceTable> sourceTables;

    /**
     * 聚合表
     */
    @TableField(typeHandler = CustomJacksonTypeHandler.class)
    private ArrangeAggregationTable aggregationTable;

    /**
     * 联表关系
     */
    @TableField(typeHandler = CustomJacksonTypeHandler.class)
    private List<JoinRelation> joinRelations;

    public AggregationTableArrangement(Integer dataModelId, ThemeArrangeRequest request) {
        this.dataModelId = dataModelId;
        this.canvas = request.getCanvas();
        this.sourceTables = request.getSourceTables();
        this.aggregationTable = request.getAggregationTable();
        this.joinRelations = request.getJoinRelations();
    }
}
