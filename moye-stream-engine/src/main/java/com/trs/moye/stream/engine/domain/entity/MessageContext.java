package com.trs.moye.stream.engine.domain.entity;

import com.fasterxml.jackson.databind.JsonNode;
import com.trs.moye.ability.domain.DataProcessLog;
import com.trs.moye.ability.domain.DataProcessRecord;
import com.trs.moye.ability.domain.DataProcessTrace;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.base.common.utils.SnowflakeIdUtil;
import com.trs.moye.stream.engine.utils.AddressHelper;
import com.trs.moye.stream.engine.utils.StreamEngineStopWatch;
import com.trs.moye.stream.engine.utils.StreamEngineStopWatch.TaskInfo;
import java.time.LocalDateTime;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * 消息执行上下文，保存建模id等额外信息
 *
 * <AUTHOR>
 * @since 2025/3/7 11:41
 */
@Data
@Builder
public class MessageContext {

    /**
     * 数据模型id
     */
    private Integer dataModelId;
    /**
     * 数据模型名称
     */
    private String dataModelName;
    /**
     * 消息id
     */
    private Long recordId;
    /**
     * 消息标题
     */
    private String title;


    /**
     * 创建默认的链路追踪数据
     *
     * @param messageContext 消息上下文
     * @return DataProcessLog
     */
    public static DataProcessLog toLog(MessageContext messageContext) {
        DataProcessLog dataProcessLog = new DataProcessLog();
        dataProcessLog.setRecordId(messageContext.getRecordId());
        dataProcessLog.setDataModelId(messageContext.getDataModelId());
        dataProcessLog.setDataModelName(messageContext.getDataModelName());
        dataProcessLog.setMsgTitle(messageContext.getTitle());
        dataProcessLog.setPodIp(AddressHelper.getServerIp());
        dataProcessLog.setStorageTime(LocalDateTime.now());
        return dataProcessLog;
    }

    /**
     * 创建全量链路追踪数据
     *
     * @param messageContext     context
     * @param stopWatch          stopwatch
     * @param totalOperatorCount 总算子数
     * @param input              输入数据
     * @param output             输出数据
     * @return DataProcessRecord
     */
    public static DataProcessRecord toRecord(MessageContext messageContext, StreamEngineStopWatch stopWatch,
        Integer totalOperatorCount, JsonNode input, JsonNode output) {
        DataProcessLog log = toLog(messageContext);
        DataProcessRecord record = new DataProcessRecord(log);
        record.setStartTime(stopWatch.getStartTime());
        record.setEndTime(stopWatch.getEndTime());
        record.setProcessingTime(stopWatch.getTotalTimeMillis());
        record.setIsError(stopWatch.isError());
        record.setErrorMsg(stopWatch.getErrorMessage());
        record.setInput(JsonUtils.toJsonString(input));
        // 将最后一个算子执行的输出作为全链路日志的输出
        record.setOutput(JsonUtils.toJsonString(output));
        record.setErrorMsgReadFlag(0);
        record.setTotalOperatorCount(totalOperatorCount);
        record.setExecuteOperatorCount(stopWatch.getExecuteOperatorCount());
        return record;
    }

    /**
     * 创建算子链路追踪数据
     *
     * @param messageContext 消息上下文
     * @param taskInfo       任务信息
     * @param order         处理顺序
     * @return DataProcessTrace
     */
    public static DataProcessTrace toTrace(MessageContext messageContext, TaskInfo taskInfo, Integer order) {
        DataProcessLog log = toLog(messageContext);
        DataProcessTrace trace = new DataProcessTrace(log);
        trace.setProcessId(SnowflakeIdUtil.newId());
        trace.setStartTime(taskInfo.getStartTime());
        trace.setEndTime(taskInfo.getEndTime());
        trace.setProcessingTime(taskInfo.getTimeMillis());
        trace.setOperatorId(taskInfo.getOperatorId());
        trace.setOperatorName(StringUtils.isBlank(taskInfo.getOperatorName())
            ? taskInfo.getTaskName() : taskInfo.getOperatorName());
        //TODO 环节内处理顺序是否要保存？
        trace.setProcessingOrder(order);
        trace.setIsError(taskInfo.getIsError());
        trace.setErrorMsg(taskInfo.getErrorMessage());
        trace.setInput(taskInfo.getInput());
        trace.setOutput(taskInfo.getOutput());
        trace.setAbilityId(taskInfo.getAbilityId());
        trace.setStorageId(taskInfo.getStorageId());
        return trace;
    }

}
