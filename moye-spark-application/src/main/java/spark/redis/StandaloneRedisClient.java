package spark.redis;

import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

/**
 * StandaloneRedisClient
 *
 * <AUTHOR>
 * @since 2025/4/29 11:31
 */
public class StandaloneRedisClient implements RedisClient {

    private final JedisPool jedisPool;

    public StandaloneRedisClient(JedisPool jedisPool) {
        this.jedisPool = jedisPool;
    }

    @Override
    public String getField(String key, String field) {
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.hget(key, field);
        }
    }

    @Override
    public Long pushElement(String key, String value) {
        try (Jedis jedis = jedisPool.getResource()) {
            Long result = jedis.rpush(key, value);
            // 设置过期时间为1天
            jedis.pexpire(key, 86400000L);
            return result;
        }
    }

    @Override
    public void publish(String channel, String message) {
        try (Jedis jedis = jedisPool.getResource()) {
            jedis.publish(channel, message);
        }
    }

    @Override
    public void close() {
        if (jedisPool != null) {
            jedisPool.close();
        }
    }
}