package com.trs.ai.moye.data.model.service.impl;

import com.trs.ai.moye.backstage.dao.NoticeSendConfInsideMapper;
import com.trs.ai.moye.backstage.entity.NoticeSendConfInside;
import com.trs.ai.moye.data.model.dto.ConnectionStoragePoints;
import com.trs.ai.moye.data.model.dto.OdsModelRequestDTO;
import com.trs.ai.moye.data.model.dto.StoragePointParams;
import com.trs.ai.moye.data.model.request.CreateTableRequests;
import com.trs.ai.moye.data.model.request.CreateTableRequests.CreateTableRequest;
import com.trs.ai.moye.data.model.request.ods.OdsFieldRequest;
import com.trs.ai.moye.data.model.request.ods.OdsModelRequest;
import com.trs.ai.moye.data.model.service.DataModelScheduleService;
import com.trs.ai.moye.data.model.service.DataModelService;
import com.trs.ai.moye.data.model.service.DwdModelService;
import com.trs.ai.moye.data.standard.dao.DataStandardFieldMapper;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.common.utils.AssertUtils;
import com.trs.moye.base.data.connection.dao.DataConnectionMapper;
import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.data.model.dao.BusinessCategoryMapper;
import com.trs.moye.base.data.model.dao.DataModelFieldMapper;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.entity.BusinessCategory;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.base.data.model.entity.DataSourceConfig;
import com.trs.moye.base.data.model.enums.CreateModeEnum;
import com.trs.moye.base.data.model.enums.ModelExecuteStatus;
import com.trs.moye.base.data.source.dao.DataSourceConfigMapper;
import com.trs.moye.base.data.standard.entity.DataStandardField;
import java.util.List;
import javax.annotation.Resource;

import com.trs.moye.base.data.storage.DataStorage;
import com.trs.moye.base.data.storage.dao.DataStorageMapper;
import com.trs.moye.base.data.storage.setting.DefaultDataStorageSettings;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * ODS 创建服务
 *
 * <AUTHOR>
 * @since 2024/12/11 15:41:55
 */
@Service
public class OdsCreateService {

    /**
     * 内置字段 trs_moye_ftp_file_name
     */
    public static final String TRS_MOYE_FTP_FILE_NAME = "trs_moye_ftp_file_name";

    @Resource
    private DataModelService dataModelService;

    @Resource
    private DwdModelService dwdModelService;

    @Resource
    private DataConnectionMapper dataConnectionMapper;

    @Resource
    private DataModelMapper dataModelMapper;

    @Resource
    private DataSourceConfigMapper dataSourceConfigMapper;

    @Resource
    private DataModelFieldMapper dataModelFieldMapper;

    @Resource
    private DataStandardFieldMapper dataStandardFieldMapper;

    @Resource
    private DataModelScheduleService dataModelScheduleService;

    @Resource
    private NoticeSendConfInsideMapper noticeSendConfInsideMapper;

    @Resource
    private BusinessCategoryMapper businessCategoryMapper;

    @Resource
    private DataStorageMapper dataStorageMapper;

    /**
     * 创建 ODS
     *
     * @param request 请求
     * @return {@link Integer }
     * <AUTHOR>
     * @since 2024/12/11 15:44:40
     */
    @Transactional(rollbackFor = Throwable.class)
    public Integer createOds(OdsModelRequestDTO request) {
        // 创建数据建模
        String modelEnName = request.getModelRequest().getModelEnName();
        modelEnName = modelEnName.replace("-", "_");
        request.getModelRequest().setModelEnName(modelEnName);
        DataModel dataModel = createDataModel(request.getBusinessCategoryId(), request.getModelRequest(),
            request.getDataSourceConnectionId());
        // 创建数据源
        final DataConnection dataConnection = createDataSource(request.getDataSourceConnectionId(), dataModel.getId(),
            request.getModelRequest());
        //创建数据存储
        //如果是选择已有的表作为存储点，需要将已有的表字段同步，以及更改建表状态，修改字段同步字段
        if (request.getStoragePointList().stream().anyMatch(e -> ObjectUtils.isNotEmpty(e.getTableNameList()))) {
            dwdModelService.addStorageInfo(request.getStoragePointList().stream()
                .map(ConnectionStoragePoints::new).toList(), dataModel);
        } else {
            // 创建建模字段
            createModelFields(dataModel, request.getModelRequest().getModelFields(), dataConnection);
            List<Integer> connectionIdList = request.getStoragePointList().stream()
                .map(StoragePointParams::getConnectionId).toList();
            // 创建数据存储
            dataModelService.createDataStorages(dataModel, connectionIdList);
        }
        // TODO 目前只有mysql类型的存储自动建表
        List<DataStorage> dataStorageList = dataStorageMapper.selectByDataModelIdWithConnection(dataModel.getId());
        List<CreateTableRequest> tableRequestList = dataStorageList.stream()
                .filter(storage -> storage.getConnection().getConnectionType() == ConnectionType.MYSQL)
                .map(storage -> {
            CreateTableRequest tableRequest = new CreateTableRequest();
            tableRequest.setConnectionId(storage.getConnectionId());
            tableRequest.setDataStorageId(storage.getId());
            DefaultDataStorageSettings settings = new DefaultDataStorageSettings();
            settings.setConnectionType(ConnectionType.MYSQL);
            return tableRequest;
        }).toList();
        dataModelService.createTable(dataModel.getId(), new CreateTableRequests(tableRequestList));
        // 创建调度配置
        dataModelScheduleService.createScheduleConfig(dataModel, request.getScheduleInfo());
        // 创建默认监控配置
        dataModelService.createDefaultMonitorConfig(dataModel.getId());
        // 创建默认执行配置
        dataModelService.createDefaultExecuteConfig(dataModel.getId(), dataConnection,
            request.getModelRequest().getModelFields());
        //创建后台消息中心日志推送配置
        BusinessCategory businessCategory = businessCategoryMapper.selectById(request.getBusinessCategoryId());
        noticeSendConfInsideMapper.insert(NoticeSendConfInside.formSystemAdd(dataModel, businessCategory));
        return dataModel.getId();
    }


    private DataModel createDataModel(Integer businessCategoryId, OdsModelRequest modelRequest, Integer connectionId) {
        DataModel model = new DataModel();
        DataModel dbModel = dataModelMapper.getByEnNameAndConnectionId(modelRequest.getModelEnName(), connectionId);
        AssertUtils.empty(dbModel, "英文名为【%s】的%s已经存在", modelRequest.getModelEnName(),
            ModelLayer.ODS.getLabel());
        model.setZhName(modelRequest.getModelZhName());
        model.setEnName(modelRequest.getModelEnName());
        model.setBusinessCategoryId(businessCategoryId);
        model.setCreateMode(CreateModeEnum.NORMAL);
        model.setLayer(ModelLayer.ODS);
        model.setExecuteStatus(ModelExecuteStatus.STOP);
        dataModelMapper.insert(model);
        return model;
    }

    private void createModelFields(DataModel dataModel, List<OdsFieldRequest> modelFields,
        DataConnection dataConnection) {
        Integer dataModelId = dataModel.getId();
        if (ObjectUtils.isNotEmpty(modelFields)) {
            for (OdsFieldRequest request : modelFields) {
                DataModelField field = request.toDataModelField();
                field.setDataModelId(dataModelId);
                dataModelFieldMapper.insert(field);
            }
        }
        // 添加内置字段
        for (DataStandardField builtInField : dataStandardFieldMapper.listBuiltInFields()) {
            if (!dataConnection.getConnectionType().isFileType() && builtInField.getEnName()
                .equals(TRS_MOYE_FTP_FILE_NAME)) {
                continue;
            }
            DataModelField field = builtInField.toDataModelField();
            field.setDataModelId(dataModelId);
            dataModelFieldMapper.insert(field);
        }
//        DataModel dataModel = dataModelMapper.getById(dataModelId);
        dataModel.setSyncField(ObjectUtils.isNotEmpty(modelFields));
        dataModelMapper.updateById(dataModel);
    }

    private DataConnection createDataSource(Integer dataSourceConnectionId, Integer dataModelId,
        OdsModelRequest modelRequest) {
        DataSourceConfig dataSource = new DataSourceConfig();
        dataSource.setDataModelId(dataModelId);
        dataSource.setConnectionId(dataSourceConnectionId);
        dataSource.setEnName(modelRequest.getSourceEnName());
        dataSource.setZhName(modelRequest.getSourceZhName());
        DataConnection dataConnection = dataConnectionMapper.selectById(dataSourceConnectionId);
        dataSource.buildSettingsByType(dataConnection, modelRequest.getDataSourceSettings());
        dataSourceConfigMapper.insert(dataSource);
        return dataConnection;
    }

}
