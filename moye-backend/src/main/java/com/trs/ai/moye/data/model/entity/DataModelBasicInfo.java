package com.trs.ai.moye.data.model.entity;

import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 数据建模基本信息
 *
 * <AUTHOR>
 * @since 2024/9/29 14:49
 */
@Data
public class DataModelBasicInfo {

    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空")
    private String zhName;

    /**
     * 英文名称
     */
    @NotBlank(message = "英文名称不能为空")
    private String enName;

    /**
     * 描述
     */
    private String description;

}
