package spark.ability.connection.connector;

import com.trs.moye.ability.entity.operator.BatchOperatorRuntimeEntity.TableInfo;
import com.trs.moye.base.common.utils.StringUtils;
import com.trs.moye.base.common.utils.conditions.SqlUtils;
import com.trs.moye.base.data.connection.entity.params.ClickhouseConnectionParams;
import com.trs.moye.base.data.model.enums.DataSaveMode;
import com.trs.moye.base.data.service.entity.Condition;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.List;
import java.util.Objects;
import java.util.Properties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SaveMode;
import org.apache.spark.sql.SparkSession;

/**
 * <AUTHOR>
 * @since 2024/10/31 10:15
 */
@Slf4j
public class CkConnector implements Connector {

    @Override
    public Dataset<Row> getInputDf(SparkSession sparkSession, TableInfo storageParams, List<Condition> conditions,
        String sql) {
        if (storageParams.getConnectionParams() instanceof ClickhouseConnectionParams) {
            ClickhouseConnectionParams ckParams = (ClickhouseConnectionParams) storageParams.getConnectionParams();
            String table = storageParams.getDbTable();

            String subquery = table;
            if (Objects.nonNull(conditions) && !conditions.isEmpty()) {
                String querySql = SqlUtils.getQuerySql(conditions, ckParams.getConnectionType(), table);
                subquery = String.format("(%s) as subquery", querySql);
            }
            if (StringUtils.isNotEmpty(sql)) {
                subquery = String.format("(%s) as subquery", sql);
            }
            storageParams.setSubquery(subquery);
            return sparkSession.read().format("jdbc")
                .option("url", ckParams.getJdbcUrl(null))
                .option("driver", ckParams.getJdbcDriverName(null))
                .option("dbtable", subquery)
                .option("user", ckParams.getUsername())
                .option("password", ckParams.getDecryptedPassword())
                .load();
        } else {
            throw new IllegalArgumentException("clickhouse connect param type wrong!");
        }
    }

    @Override
    public void save(SparkSession sparkSession, Dataset<Row> df, TableInfo storageParams) {
        if (storageParams.getConnectionParams() instanceof ClickhouseConnectionParams) {
            ClickhouseConnectionParams params = (ClickhouseConnectionParams) storageParams.getConnectionParams();
            Properties properties = new Properties();
            properties.put("user", params.getUsername());
            properties.put("password", params.getDecryptedPassword());
            properties.put("driver", params.getJdbcDriverName(null));
            String jdbcUrl = params.getJdbcUrl(null);

            // 先清理分区
            if (CollectionUtils.isNotEmpty(storageParams.getDeletePartitions())) {
                deletePartitions(storageParams, params);
            }

            // 写入数据
            df.write()
                .mode(DataSaveMode.DROP_DATA.equals(storageParams.getSaveMode()) ? SaveMode.Overwrite : SaveMode.Append)
                .jdbc(jdbcUrl, storageParams.getDbTable(), properties);
        } else {
            throw new IllegalArgumentException("clickhouse connect type wrong!");
        }
    }

    private static void deletePartitions(TableInfo storageParams, ClickhouseConnectionParams params) {
        try (Connection connection = DriverManager.getConnection(params.getJdbcUrl(null), params.getUsername(),
            params.getDecryptedPassword())) {

            for (String partition : storageParams.getDeletePartitions()) {
                String deletePartitionSql = String.format("ALTER TABLE %s DROP PARTITION '%s'",
                    storageParams.getDbTable(), partition);
                try (Statement stmt = connection.createStatement()) {
                    stmt.execute(deletePartitionSql);
                    log.info("Successfully dropped partition: {}", partition);
                } catch (SQLException e) {
                    log.error("Failed to drop partition: {}", partition, e);
                    throw new RuntimeException("Partition drop failed for " + partition, e);
                }
            }
        } catch (SQLException e) {
            log.error("Failed to connect to ClickHouse", e);
            throw new RuntimeException("Database connection failed", e);
        }
    }
}

