package spark.ability.stream;

import com.fasterxml.jackson.databind.JsonNode;
import com.trs.moye.ability.aviator.AviatorService;
import com.trs.moye.ability.aviator.ExecuteOperator;
import com.trs.moye.ability.entity.operator.BatchOperatorRuntimeEntity;
import com.trs.moye.ability.entity.operator.Operator;
import com.trs.moye.ability.entity.operator.OperatorRowType;
import com.trs.moye.base.common.utils.JsonUtils;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.spark.api.java.function.MapFunction;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Encoder;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;
import org.apache.spark.sql.catalyst.expressions.GenericRowWithSchema;
import org.apache.spark.sql.types.DataType;
import org.apache.spark.sql.types.DataTypes;
import spark.ability.base.Ability;
import spark.ability.base.AbilityInterface;
import spark.entity.TaskInfo;
import spark.exception.FailedOperatorException;
import spark.exception.ParamValidationException;

/**
 * StreamAbilitySdk
 *
 * <AUTHOR>
 * @since 2025/3/13 10:18
 */
@Slf4j
public class StreamAbilitySdk extends Ability implements AbilityInterface {

    /**
     * 执行能力
     *
     * @param sparkSession spark session
     * @param operator     能力执行参数
     * @param taskInfo     批次号
     * @return data frame
     */
    @Override
    public Dataset<Row> process(SparkSession sparkSession, BatchOperatorRuntimeEntity operator, TaskInfo taskInfo) {
        Dataset<Row> resourceDf = sparkSession.table(operator.getInputTables().values().iterator().next());

        // 流处理算子
        Operator streamOperator = operator.toOperator();

        // 输出字段
        OperatorRowType fields = operator.getOutputFields();
        Encoder<Row> resultEncoder = getResultEncoder(fields);

        return resourceDf.map((MapFunction<Row, Row>) row -> {
            // aviator 初始化
            AviatorConfig.init();
            // Row转换成JsonNode
            String rowJsonString = row.json();
            log.debug("rowJsonString: {}", rowJsonString);
            JsonNode rowJson = JsonUtils.toJsonNode(rowJsonString);
            log.debug("rowJson: {}", rowJson);

            List<Object> resultValues = new ArrayList<>();
            try {
                JsonNode resultData = rowJson;
                // 执行算子
<<<<<<< HEAD
                JsonNode resultData = streamOperator.execute(rowJson, 15);
=======
                ExecuteOperator executeOperator = new ExecuteOperator(streamOperator);
                if (AviatorService.executeAviatorScript(resultData, executeOperator.getScript())) {
                    log.debug("满足条件并执行算子: {}", resultData);
                    resultData = streamOperator.execute(resultData, 15);
                }
>>>>>>> test
                log.debug("resultData: {}", resultData);

                // JsonNode 第一层字段转换成Row
                for (String field : resultEncoder.schema().fieldNames()) {

                    DataType dataType = resultEncoder.schema().apply(field).dataType();
                    log.debug("dataType: {}", dataType);

                    JsonNode valueNode = resultData.get(field);
                    log.debug("valueNode: {}", valueNode);

                    resultValues.add(convertValue(valueNode, dataType));
                }
            } catch (Throwable e) {
                throw new FailedOperatorException(
                    String.format("算子: %s 执行失败, 数据: %s", streamOperator.getName(), rowJsonString), e);
            }

            Object[] result = resultValues.toArray(new Object[0]);
            log.debug("resultValues: {}", JsonUtils.toJsonString(result));
            log.debug("structType: {}", resultEncoder.schema().prettyJson());
            return new GenericRowWithSchema(result, resultEncoder.schema());
        }, resultEncoder);
    }


    private static Object convertValue(JsonNode valueNode, DataType dataType) {
        if (valueNode == null || valueNode.isNull()) {
            return null;
        } else if (dataType.equals(DataTypes.IntegerType)) {
            return JsonUtils.convertValue(valueNode, Integer.class);
        } else if (dataType.equals(DataTypes.LongType)) {
            return JsonUtils.convertValue(valueNode, Long.class);
        } else if (dataType.equals(DataTypes.FloatType)) {
            return JsonUtils.convertValue(valueNode, Float.class);
        } else if (dataType.equals(DataTypes.DoubleType)) {
            return JsonUtils.convertValue(valueNode, Double.class);
        } else if (dataType.equals(DataTypes.BooleanType)) {
            return JsonUtils.convertValue(valueNode, Boolean.class);
        } else if (dataType.equals(DataTypes.ShortType)) {
            return JsonUtils.convertValue(valueNode, Short.class);
        }

        // 其余类型将 jsonNode 转字符串
        return JsonUtils.nodeToString(valueNode);
    }


    /**
     * 校验参数
     *
     * @param operator 参数
     */
    @Override
    public void validate(BatchOperatorRuntimeEntity operator) throws ParamValidationException {
        validateInputTables(operator.getInputTables(), 1, 1);
    }
}
