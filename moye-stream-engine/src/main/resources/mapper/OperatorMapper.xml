<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.moye.stream.engine.dao.OperatorMapper">
    <resultMap id="OperatorMap" type="com.trs.moye.ability.entity.operator.Operator">
        <id column="id" property="id"/>
        <result column="pipeline_id" property="pipelineId"/>
        <result column="name" property="name"/>
        <result column="input_fields" property="inputFields"
            typeHandler="com.trs.moye.ability.typehandler.OperatorRowTypeHandler"/>
        <result column="output_fields" property="outputFields"
            typeHandler="com.trs.moye.ability.typehandler.OperatorRowTypeHandler"/>
        <result column="ability_id" property="abilityId"/>
        <result column="storage_id" property="storageId"/>
        <result column="input_bind" property="inputBind"
            typeHandler="com.trs.moye.ability.typehandler.InputBindTypeHandler"/>
        <result column="output_bind" property="outputBind"
            typeHandler="com.trs.moye.ability.typehandler.OutputBindTypeHandler"/>
        <result column="conditions" property="conditions"
            typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler"/>
        <association column="ability_id" property="ability"
            javaType="com.trs.moye.ability.entity.Ability"
            select="com.trs.moye.stream.engine.dao.AbilityMapper.selectById"/>
    </resultMap>

    <select id="selectByPipelineId" resultMap="OperatorMap">
        SELECT *
        FROM operator
        WHERE pipeline_id = #{id}
    </select>
</mapper>