package com.trs.ai.moye.data.model.request;

import com.trs.ai.moye.data.model.entity.DataModelBasicInfo;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.enums.CreateModeEnum;
import com.trs.moye.base.data.model.enums.ModelExecuteStatus;
import com.trs.moye.base.data.model.request.arrangement.theme.ThemeArrangeRequest;
import com.trs.moye.base.data.schedule.ScheduleInfo;
import java.util.Set;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025-07-31 15:50
 */
@Data
@NoArgsConstructor
public class ThemeCreateRequest {

    /**
     * 基础信息
     */
    @NotNull(message = "基本信息不能为空")
    private DataModelBasicInfo basicInfo;

    /**
     * 模型层级
     */
    @NotNull(message = "模型层级不能为空")
    private ModelLayer modelLayer;


    /**
     * 业务分类id
     */
    @NotNull(message = "业务分类id不能为空")
    private Integer businessCategoryId;

    /**
     * 编排信息
     */
    @NotNull(message = "编排信息不能为空")
    @Valid
    private ThemeArrangeRequest arrangeInfo;

    /**
     * 存储点id列表，（数据连接id列表）
     */
    @NotEmpty(message = "存储点列表不能为空")
    private Set<Integer> dataStorageIds;

    /**
     * 调度信息
     */
    @NotNull(message = "接入频率不能为空")
    @Valid
    private ScheduleInfo scheduleInfo;

    /**
     * 转换为数据模型对象
     *
     * @return {@link DataModel} 数据建模
     */
    public DataModel toDataModel() {
        DataModel model = new DataModel();
        model.setEnName(basicInfo.getEnName());
        model.setZhName(basicInfo.getZhName());
        model.setLayer(modelLayer);
        model.setBusinessCategoryId(businessCategoryId);
        model.setDescription(basicInfo.getDescription());
        model.setCreateMode(CreateModeEnum.NORMAL);
        model.setExecuteStatus(ModelExecuteStatus.STOP);
        model.setSyncField(true);
        model.setIsArranged(true);
        return model;
    }
}
