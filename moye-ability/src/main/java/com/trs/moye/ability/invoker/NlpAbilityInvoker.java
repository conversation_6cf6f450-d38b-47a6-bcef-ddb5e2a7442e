package com.trs.moye.ability.invoker;

import com.fasterxml.jackson.databind.JsonNode;
import com.trs.moye.ability.base.nlp.NlpAbility;
import com.trs.moye.ability.base.nlp.entity.EntityQueryService;
import com.trs.moye.ability.base.nlp.process.NlpEngineService;
import com.trs.moye.ability.entity.Ability;
import com.trs.moye.ability.entity.InputBind;
import com.trs.moye.ability.entity.Schema;
import com.trs.moye.ability.entity.Schema.ObjectTypeSchema;
import com.trs.moye.ability.exception.AbilityInvokeException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

/**
 * nlp算子调用
 *
 * <AUTHOR>
 * @since 2025/3/27 14:45
 */
public class NlpAbilityInvoker extends AbstractLocalAbilityInvoker implements AbilityInvoker {

    private final NlpEngineService nlpEngineService;

    private final EntityQueryService entityQueryService;

    public NlpAbilityInvoker(NlpEngineService nlpEngineService, EntityQueryService entityQueryService) {
        this.nlpEngineService = nlpEngineService;
        this.entityQueryService = entityQueryService;
    }


    @Override
    public Object invoke(Ability ability, JsonNode input, InputBind inputBind) throws Throwable {
        NlpAbility nlpAbility = new NlpAbility(nlpEngineService, entityQueryService);
        try {
            // 1. 获取目标类和方法
            String path = ability.getPath();
            String[] parts = path.split("\\.");
            // 修正：手动拼接类名
            String methodName = parts[parts.length - 1];

            Method method = findMethod(methodName, ability.getInputSchema());

            // 2. 绑定输入数据
            Object[] args = bindParameters(method, input, inputBind);

            // 3. 反射调用方法
            return method.invoke(nlpAbility, args);

        } catch (InvocationTargetException ite) {
            //抛出调用方法的异常
            throw ite.getTargetException();
        } catch (Exception e) {
            throw new AbilityInvokeException(
                "调用NLP算子失败: " + ability.getPath() + "\n"
                    + " [input]: " + inputBind.getParamsMap(input, ability.getInputSchema()) + "\n"
                    + " [dataBind]: " + inputBind + "\n"
                    + " [because]: " + e.getMessage(), e);
        }
    }

    /**
     * 查找目标方法
     *
     * @param methodName  方法名称
     * @param inputSchema 输入参数模式
     * @return 目标方法
     * @throws NoSuchMethodException 如果方法未找到
     */
    private static Method findMethod(String methodName, Schema inputSchema)
        throws NoSuchMethodException {
        for (Method method : NlpAbility.class.getDeclaredMethods()) {
            if (method.getName().equals(methodName)
                && inputSchema instanceof ObjectTypeSchema
                && method.getParameterCount() == ((ObjectTypeSchema) inputSchema).getProperties().size()) {
                return method;
            }

        }
        throw new NoSuchMethodException("Method not found: " + methodName);
    }
}
