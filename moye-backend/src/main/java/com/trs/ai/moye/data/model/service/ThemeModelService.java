package com.trs.ai.moye.data.model.service;

import com.trs.ai.moye.data.model.request.ThemeCreateRequest;
import com.trs.ai.moye.data.model.response.ThemeModelSourceResponse;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.data.model.entity.AggregationTableArrangement;
import com.trs.moye.base.data.model.request.arrangement.theme.ThemeArrangeRequest;
import java.util.List;

/**
 * 主题库建模服务
 *
 * <AUTHOR>
 * @since 2025-07-31 11:32
 */
public interface ThemeModelService {

    /**
     * 获取主题库建模来源列表
     *
     * @param layer 模型层
     * @return 来源列表
     */
    List<ThemeModelSourceResponse> listModelSources(ModelLayer layer);

    /**
     * 添加主题
     *
     * @param request 请求参数
     * @return 建模id
     */
    int addTheme(ThemeCreateRequest request);

    /**
     * 查询编排信息，并设置建表状态
     *
     * @param id 数据模型id
     * @return 编排信息
     */
    AggregationTableArrangement getArrange(Integer id);

    /**
     * 更新主题编排信息
     *
     * @param id      建模id
     * @param request 请求参数
     */
    void updateArrange(Integer id, ThemeArrangeRequest request);
}
