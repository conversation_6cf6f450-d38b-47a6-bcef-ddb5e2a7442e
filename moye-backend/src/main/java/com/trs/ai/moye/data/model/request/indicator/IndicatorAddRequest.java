package com.trs.ai.moye.data.model.request.indicator;

import com.trs.ai.moye.data.model.entity.DataModelBasicInfo;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.common.request.PageParams;
import com.trs.moye.base.data.indicator.entity.DataModelIndicatorField;
import com.trs.moye.base.data.indicator.entity.IndicatorConfig;
import com.trs.moye.base.data.indicator.entity.IndicatorStatisticStrategyInfo;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.enums.CreateModeEnum;
import com.trs.moye.base.data.model.enums.ModelExecuteStatus;
import com.trs.moye.base.data.service.entity.Condition;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 指示器添加请求
 *
 * <AUTHOR>
 * @since 2025/05/15 18:11:34
 */
@Data
public class IndicatorAddRequest {

    /**
     * 业务分类id
     */
    @NotNull(message = "业务分类id不能为空")
    private Integer businessCategoryId;

    /**
     * 分层
     */
    private ModelLayer modelLayer;

    /**
     * 基本信息
     */
    private DataModelBasicInfo basicInfo;

    /**
     * 数据来源ID
     */
    private Integer dataSourceId;

    /**
     * 统计策略信息
     */
    private IndicatorStatisticStrategyInfo statisticStrategyInfo;

    /**
     * 条件
     */
    private List<Condition> conditions;

    /**
     * 统计 SQL
     */
    private String statisticSql;

    /**
     * 字段列表
     */
    private List<DataModelIndicatorField> fields;

    /**
     * 预览-页面参数
     */
    private PageParams pageParams;

    /**
     * 构建实体
     *
     * @return {@link DataModel }
     */
    public DataModel toModel() {
        DataModel dataModel = new DataModel();
        //基本信息
        dataModel.setZhName(basicInfo.getZhName());
        dataModel.setEnName(basicInfo.getEnName());
        dataModel.setDescription(basicInfo.getDescription());
        //业务分类id
        dataModel.setBusinessCategoryId(businessCategoryId);
        // 创建模式默认元数据建模，如果有元数据标准id则是元数据标准
        dataModel.setCreateMode(CreateModeEnum.NORMAL);
        //调度状态初始化为关闭
        dataModel.setExecuteStatus(ModelExecuteStatus.STOP);
        //分层--要素库
        dataModel.setLayer(modelLayer);
        //是否治理默认为否
        dataModel.setIsArranged(false);
        return dataModel;
    }

    /**
     * 构建指标配置
     *
     * @param dataModelId 数据建模id
     * @return {@link IndicatorConfig }
     * <AUTHOR>
     * @since 2025/05/20 17:28:10
     */
    public IndicatorConfig toIndicatorConfig(Integer dataModelId) {
        IndicatorConfig indicatorConfig = new IndicatorConfig();
        //数据建模id
        indicatorConfig.setDataModelId(dataModelId);
        //统计策略
        indicatorConfig.setStatisticStrategyInfo(statisticStrategyInfo);
        //业务逻辑条件
        indicatorConfig.setConditions(conditions.toArray(new Condition[0]));
        //统计sql
        indicatorConfig.setStatisticSql(statisticSql);
        return indicatorConfig;
    }
}
