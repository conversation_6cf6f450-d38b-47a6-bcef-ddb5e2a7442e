package com.trs.moye.base.data.model.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.moye.base.data.model.entity.AggregationTableArrangement;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @since 2025-07-31 17:49
 */
@Mapper
public interface AggregationTableArrangementMapper extends BaseMapper<AggregationTableArrangement> {

    /**
     * 根据模型ID获取聚合表编排
     *
     * @param dataModelId 模型ID
     * @return 聚合表编排
     */
    AggregationTableArrangement getByModelId(@Param("dataModelId") Integer dataModelId);
}
