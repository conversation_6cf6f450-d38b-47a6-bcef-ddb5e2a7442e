package com.trs.ai.moye.data.model.request.view;

import com.trs.ai.moye.data.model.entity.view.JoinRelationInfo;
import com.trs.moye.base.data.model.enums.JoinType;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 联表关系
 *
 * <AUTHOR>
 * @since 2024/9/23 14:50
 */
@Data
public class JoinRelationRequest {

    /**
     * join的主表中文名
     */
    private String sourceTableZhName;

    /**
     * join的主表英文名
     */
    @NotBlank
    private String sourceTableEnName;

    /**
     * 主表join字段英文名
     */
    @NotBlank
    private String sourceField;

    /**
     * 连接表join字段英文名
     */
    @NotBlank
    private String targetField;

    /**
     * join的连接表中文名
     */
    private String targetTableZhName;

    /**
     * join的连接表英文名
     */
    @NotBlank
    private String targetTableEnName;

    /**
     * join类型
     */
    @NotNull
    private JoinType joinType;

    /**
     * 构建JoinRelationInfo
     *
     * @return {@link JoinRelationInfo}
     */
    public JoinRelationInfo toJoinRelationInfo() {
        JoinRelationInfo joinRelationInfo = new JoinRelationInfo();
        joinRelationInfo.setJoinType(joinType);
        joinRelationInfo.setSourceTable(sourceTableEnName);
        joinRelationInfo.setSourceField(sourceField);
        joinRelationInfo.setTargetTable(targetTableEnName);
        joinRelationInfo.setTargetField(targetField);
        return joinRelationInfo;
    }
}
