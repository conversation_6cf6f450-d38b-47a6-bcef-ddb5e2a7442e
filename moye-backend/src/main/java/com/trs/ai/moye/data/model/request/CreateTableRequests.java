package com.trs.ai.moye.data.model.request;

import com.trs.moye.base.data.storage.setting.DataStorageSettings;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 建表请求参数
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CreateTableRequests {

    /**
     * 参数：key 为数据源id，value 为数据源配置
     */
    private List<CreateTableRequest> tables;

    /**
     * 建表请求参数
     */
    @Data
    public static class CreateTableRequest {

        /**
         * 连接id
         */
        private Integer connectionId;

        /**
         * 存储id
         */
        private Integer dataStorageId;

        /**
         * 建表参数
         */
        private DataStorageSettings settings;
    }
}
