package spark.redis;

import java.util.HashSet;
import java.util.Set;
import redis.clients.jedis.HostAndPort;
import redis.clients.jedis.JedisCluster;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

/**
 * JedisConnection
 *
 * <AUTHOR>
 * @since 2025/2/10 10:57
 */
public class JedisConnection {

    private static RedisClient redisClient;

    private JedisConnection() {
    }

    public static void init(String host, int port, String password, int database) {
        boolean isClusterMode = isCluster(host);

        if (isClusterMode) {
            Set<HostAndPort> clusterNodes = parseClusterNodes(host);
            JedisCluster jedisCluster = new JedisCluster(clusterNodes, 2000, 2000, 5, password, new JedisPoolConfig());
            redisClient = new ClusterRedisClient(jedisCluster);
        } else {
            JedisPool jedisPool = new JedisPool(new JedisPoolConfig(), host, port, 2000, password, database);
            redisClient = new StandaloneRedisClient(jedisPool);
        }
    }

    private static boolean isCluster(String host) {
        // 留白，供你通过 host 判断是否为集群模式
        return host.contains(":") || host.contains(",");
    }

    private static Set<HostAndPort> parseClusterNodes(String host) {
        Set<HostAndPort> clusterNodes = new HashSet<>();
        String[] nodes = host.split(",");
        for (String node : nodes) {
            String[] parts = node.split(":");
            clusterNodes.add(new HostAndPort(parts[0], Integer.parseInt(parts[1])));
        }
        return clusterNodes;
    }

    public static String getField(String key, String field) {
        return redisClient.getField(key, field);
    }

    public static Long pushElementAndNotify(String key, String value, String channel) {
        Long result = redisClient.pushElement(key, value);
        redisClient.publish(channel, key + "::" + result);
        return result;
    }

    public static void closePool() {
        redisClient.close();
    }
}
