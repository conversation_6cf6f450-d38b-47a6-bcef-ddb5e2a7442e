package spark.ability.connection.connector;

import com.trs.moye.ability.entity.operator.BatchOperatorRuntimeEntity.TableInfo;
import com.trs.moye.base.common.utils.conditions.SqlUtils;
import com.trs.moye.base.data.connection.entity.params.HiveConnectionParams;
import com.trs.moye.base.data.model.enums.DataSaveMode;
import com.trs.moye.base.data.service.entity.Condition;
import java.util.List;
import java.util.Objects;
import java.util.Properties;
import lombok.extern.slf4j.Slf4j;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SaveMode;
import org.apache.spark.sql.SparkSession;
import spark.config.PropertiesManager;

/**
 * hive
 *
 * <AUTHOR>
 * @since 2024/10/31 10:13
 */
@Slf4j
public class HiveConnector implements Connector {

    @Override
    public Dataset<Row> getInputDf(SparkSession sparkSession, TableInfo storageParams, List<Condition> conditions,
        String sql) {
        if (storageParams.getConnectionParams() instanceof HiveConnectionParams) {
            HiveConnectionParams params = (HiveConnectionParams) storageParams.getConnectionParams();
            String table = storageParams.getDbTable();

            // hive是否与spark在同集群中，线上一般在同集群中，开发环境不是
            if (PropertiesManager.isHiveInSparkCluster()) {
                return getInputDfInternalHive(sparkSession, params, table, conditions, storageParams);
            }
            return getInputDfExternalHive(sparkSession, params, table, conditions, storageParams);
        } else {
            throw new IllegalArgumentException("hive connect param type wrong!");
        }
    }

    private Dataset<Row> getInputDfInternalHive(SparkSession sparkSession, HiveConnectionParams params, String table,
        List<Condition> conditions, TableInfo tableInfo) {
        if (Objects.nonNull(conditions) && !conditions.isEmpty()) {
            String querySql = SqlUtils.getQuerySql(conditions, params.getConnectionType(), params.getDatabase(), table);
            tableInfo.setSubquery(querySql);
            return sparkSession.sql(querySql);
        }
        return sparkSession.read().table(params.getDatabase() + "." + table);
    }

    private Dataset<Row> getInputDfExternalHive(SparkSession sparkSession, HiveConnectionParams params, String table,
        List<Condition> conditions, TableInfo tableInfo) {
        String subquery = table;
        if (Objects.nonNull(conditions) && !conditions.isEmpty()) {
            String querySql = SqlUtils.getQuerySql(conditions, params.getConnectionType(), table);
            subquery = String.format("(%s) as subquery", querySql);
        }
        tableInfo.setSubquery(subquery);
        return sparkSession.read().format("jdbc")
            .option("url", params.getJdbcUrl(null))
            .option("dbtable", subquery)
            .option("user", params.getUsername())
            .option("password", params.getDecryptedPassword())
            .load();
    }


    @Override
    public void save(SparkSession sparkSession, Dataset<Row> df, TableInfo storageParams) {
        if (storageParams.getConnectionParams() instanceof HiveConnectionParams) {
            HiveConnectionParams params = (HiveConnectionParams) storageParams.getConnectionParams();
            Properties properties = new Properties();
            properties.put("user", params.getUsername());
            properties.put("password", params.getDecryptedPassword());
            String jdbcUrl = params.getJdbcUrl(storageParams.getKerberosCertificate());
            df.write()
                .mode(DataSaveMode.DROP_DATA.equals(storageParams.getSaveMode()) ? SaveMode.Overwrite : SaveMode.Append)
                .jdbc(jdbcUrl, storageParams.getDbTable(), properties);
        } else {
            throw new IllegalArgumentException("hive connect type wrong!");
        }
    }
}
