package com.trs.ai.moye.data.service.response;

import com.trs.ai.moye.data.service.enums.DbTypeMap;
import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.connection.entity.params.ConnectionParams;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数据库详情响应
 *
 * <AUTHOR>
 * @since 2024/10/12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DbInfoResponse {

    /**
     * 数据库ID
     */
    private Integer id;
    /**
     * 数据源连接名称
     */
    private String name;
    /**
     * 数据库类型id
     */
    private Integer dbType;
    /**
     * 参数
     */
    private ConnectionParams params;
    /**
     * 构造函数
     *
     * @param dbInfo 数据库连接信息
     */
    public DbInfoResponse(DataConnection dbInfo) {
        this.id = dbInfo.getId();
        this.name = dbInfo.getName();
        this.dbType = DbTypeMap.getDbTypeId(dbInfo.getConnectionType());
        this.params = dbInfo.getConnectionParams();
    }
}
