<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.moye.base.data.model.dao.DataModelMapper">
    <!-- 结果集映射 -->
    <resultMap id="DataModelMap" type="com.trs.moye.base.data.model.entity.DataModel">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_mode" jdbcType="VARCHAR" property="createMode"/>
        <result column="execute_status" jdbcType="VARCHAR" property="executeStatus"/>
        <result column="zh_name" jdbcType="VARCHAR" property="zhName"/>
        <result column="en_name" jdbcType="VARCHAR" property="enName"/>
        <result column="business_category_id" jdbcType="INTEGER" property="businessCategoryId"/>
        <result column="layer" jdbcType="VARCHAR" property="layer"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="is_arranged" jdbcType="BOOLEAN" property="isArranged"/>
        <result column="is_sync_field" jdbcType="BOOLEAN" property="syncField"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="meta_data_standard_id" jdbcType="BIGINT" property="metaDataStandardId"/>
        <!--执行参数-->
        <association column="id" property="executeConfig"
            javaType="com.trs.moye.base.data.model.entity.DataModelExecuteConfig"
            typeHandler="com.trs.moye.base.common.typehandler.PolymorphismTypeHandler"
            select="com.trs.moye.base.data.model.dao.DataModelExecuteConfigMapper.selectByDataModelId"/>
        <!--调度参数-->
        <association column="id" property="scheduleConfig"
            javaType="com.trs.moye.base.data.model.entity.DataModelScheduleConfig"
            typeHandler="com.trs.moye.base.common.typehandler.PolymorphismTypeHandler"
            select="com.trs.moye.base.data.model.dao.DataModelScheduleConfigMapper.selectByDataModelId"/>
        <!--数据源-->
        <collection property="dataSource" ofType="com.trs.moye.base.data.model.entity.DataSourceConfig"
            select="com.trs.moye.base.data.source.dao.DataSourceConfigMapper.selectByDataModel" column="id"/>
        <!--存储-->
        <collection property="dataStorages" ofType="com.trs.moye.base.data.storage.DataStorage">
            <id property="id" column="data_storage_id"/>
            <result property="enName" column="data_storage_name"/>
            <result property="zhName" column="data_storage_comment"/>
            <result property="saveMode" column="data_storage_save_mode"/>
            <result property="createTableStatus" column="data_storage_status"/>
            <result property="settings" column="data_storage_settings"
                typeHandler="com.trs.moye.base.data.storage.setting.DataStorageSettingsTypeHandler"/>
            <result property="fieldIds" column="data_storage_fieldIds"
                typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler"/>
            <result property="connectionId" column="data_storage_connection_id"/>
            <association column="data_storage_connection_id" property="connection"
                javaType="com.trs.moye.base.data.connection.entity.DataConnection"
                select="com.trs.moye.base.data.connection.dao.DataConnectionMapper.selectById"/>
        </collection>
        <!--字段-->
        <collection property="fields" ofType="com.trs.moye.base.data.model.entity.DataModelField"
            select="com.trs.moye.base.data.model.dao.DataModelFieldMapper.selectByDataModelId" column="id"
            typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler"/>
    </resultMap>

    <resultMap id="SearchDataModelInfoMap" type="com.trs.moye.base.data.model.entity.SearchDataModelInfo">
        <result column="id" property="id"/>
        <result column="categoryName" property="categoryName"/>
        <result column="categoryDesc" property="categoryDesc"/>
        <result column="zhName" property="zhName"/>
        <result column="enName" property="enName"/>
        <result column="description" property="description"/>
        <collection property="fields" ofType="com.trs.moye.base.data.model.entity.DataModelField"
            select="com.trs.moye.base.data.model.dao.DataModelFieldMapper.selectByDataModelId" column="id"/>
    </resultMap>

    <resultMap id="DataModelConnDetailMap" type="com.trs.moye.base.data.model.entity.DataModelConnDetail">
        <result column="id" property="id"/>
        <result column="zh_name" property="zhName"/>
        <result column="en_name" property="enName"/>
        <result column="connection_params" property="storageConnectionParams"
            typeHandler="com.trs.moye.base.common.typehandler.PolymorphismTypeHandler"/>
    </resultMap>
    <!-- 结果集映射 -->
    <resultMap id="BaseResultMap" type="com.trs.moye.base.data.model.entity.DataModel">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_mode" jdbcType="VARCHAR" property="createMode"/>
        <result column="execute_status" jdbcType="VARCHAR" property="executeStatus"/>
        <result column="zh_name" jdbcType="VARCHAR" property="zhName"/>
        <result column="en_name" jdbcType="VARCHAR" property="enName"/>
        <result column="business_category_id" jdbcType="INTEGER" property="businessCategoryId"/>
        <result column="layer" jdbcType="VARCHAR" property="layer"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="is_arranged" jdbcType="BOOLEAN" property="isArranged"/>
        <result column="is_sync_field" jdbcType="BOOLEAN" property="syncField"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="meta_data_standard_id" jdbcType="BIGINT" property="metaDataStandardId"/>
    </resultMap>


    <update id="updateBusinessCategoryIdByIds">
        UPDATE data_model
        SET business_category_id = #{businessId} WHERE id IN
        <foreach collection="dataModelIds" item="dataModelId" open="(" separator="," close=")">
            #{dataModelId}
        </foreach>
    </update>
    <update id="updateIsSyncProperty">
        UPDATE data_model
        SET is_sync_field = #{aTrue}
        WHERE id = #{dataModelId}
    </update>

    <update id="updateIsArranged">
        update data_model
        set is_arranged = #{isArranged}
        where id = #{metadataId}
    </update>

    <select id="listByDataSourceIdCollection" resultMap="DataModelMap">
        select dm.*
        from data_model dm
        join data_source_config ds on dm.id = ds.data_model_id
        <where>
            <choose>
                <when test="dataSourceIdCollection == null or dataSourceIdCollection.isEmpty">
                    and false
                </when>
                <otherwise>
                    and ds.id in
                    <foreach close=")" collection="dataSourceIdCollection" item="dataSourceId" open="(" separator=",">
                        #{dataSourceId,jdbcType=BIGINT}
                    </foreach>
                </otherwise>
            </choose>
        </where>
    </select>

    <select id="selectById" resultMap="DataModelMap">
        select m.id,
               m.create_mode,
               m.execute_status,
               m.zh_name,
               m.en_name,
               m.business_category_id,
               m.layer,
               m.description,
               m.create_by,
               m.create_time,
               m.update_by,
               m.update_time,
               m.is_arranged,
               s.id                  as data_storage_id,
               s.en_name             as data_storage_name,
               s.zh_name             as data_storage_comment,
               s.save_mode           as data_storage_save_mode,
               s.create_table_status as data_storage_status,
               s.field_ids           as data_storage_fieldIds,
               s.connection_id       as data_storage_connection_id,
               s.settings            as data_storage_settings
        from data_model m
                 left outer join data_storage s on m.id = s.data_model_id
        where m.id = #{id}
    </select>

    <select id="selectWithDataStorageByIds" resultMap="DataModelMap">
        select m.id,
               m.create_mode,
               m.execute_status,
               m.zh_name,
               m.en_name,
               m.business_category_id,
               m.layer,
               m.description,
               m.create_by,
               m.create_time,
               m.update_by,
               m.update_time,
               m.is_arranged,
               m.meta_data_standard_id,
               s.id                  as data_storage_id,
               s.en_name             as data_storage_name,
               s.zh_name             as data_storage_comment,
               s.save_mode           as data_storage_save_mode,
               s.create_table_status as data_storage_status,
               s.field_ids           as data_storage_fieldIds,
               s.connection_id       as data_storage_connection_id
        from data_model m
        left outer join data_storage s on m.id = s.data_model_id
        where m.id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="standardFieldDependList" resultMap="DataModelMap">
        select *
        from data_model
        where id in (select data_model_id
                     from data_model_field
                     where en_name = #{standardFieldEnName}
                       and `is_use_standard` = 1)
    </select>

    <select id="listByStandardFieldEnName" resultMap="DataModelMap">
        select *
        from data_model
        where id in (select data_model_id
                     from data_model_field
                     where en_name = #{standardFieldEnName}
                       and `is_use_standard` = 1)
    </select>
    <select id="selectByIds" resultMap="DataModelMap">
        select *
        from data_model
        <where>
            <choose>
                <when test="dataModelIds != null and !dataModelIds.isEmpty()">
                    id in
                    <foreach collection="dataModelIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </when>
                <otherwise>
                    false
                </otherwise>
            </choose>
        </where>
    </select>

    <select id="getById" resultMap="DataModelMap">
        select *
        from data_model
        where id = #{id}
    </select>
    <select id="getByEnName" resultMap="DataModelMap">
        select *
        from data_model
        where en_name = #{enName}
        limit 1
    </select>
    <select id="selectByDataModelSourceId" resultMap="DataModelMap">
        select dm.*
        from data_source_config ds
                 join data_model dm on ds.data_model_id = dm.id
        where ds.source_model_id = #{dataModelId}
    </select>
    <select id="selectByCategoryId" resultMap="DataModelMap">
        select *
        from data_model
        where business_category_id = #{id}
    </select>
    <select id="countByMetaDataStandardId" resultType="java.lang.Integer">
        select count(1)
        from data_model
        where meta_data_standard_id = #{metadataStandardId}
    </select>
    <select id="existsByEnName" resultType="java.lang.Boolean">
        select count(*)
        from data_model
        where `en_name` = #{enName}
    </select>
    <select id="selectByDataModelIds" resultMap="DataModelConnDetailMap">
        SELECT dc.connection_params,
               dm.id,
               dm.zh_name,
               dm.en_name
        FROM data_model dm
                 JOIN data_source_config ds ON dm.id = ds.data_model_id
                 JOIN data_connection dc ON ds.connection_id = dc.id
        WHERE
        dm.id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectStartStreamProcessTask" resultMap="DataModelMap">
        select dm.*
        from data_model dm
                 inner join data_model_schedule_config dmsc on (dm.id = dmsc.data_model_id)
        where dm.layer = 'DWD'
          and dmsc.execute_mode = 'REALTIME'
          and execute_status = 'START'
    </select>


    <select id="selectByLayerAndKeyword" resultType="com.trs.moye.base.data.model.entity.DataModel">
        select *
        from data_model
        where layer = #{layer}
        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(keyword)">
            <bind name="value" value="'%' + keyword + '%'"/>
            and (zh_name like #{value} or en_name like #{value})
        </if>
    </select>

    <select id="selectByZhName" resultType="com.trs.moye.base.data.model.entity.DataModel">
        select *
        from data_model
        where zh_name like concat('%', #{zhName}, '%')
          and layer = 'ODS';
    </select>
    <select id="selectStreamProcessTask" resultMap="DataModelMap">
        select dm.*
        from data_model dm
                 inner join data_model_schedule_config dmsc on (dm.id = dmsc.data_model_id)
        where dm.layer = 'DWD'
          and dmsc.execute_mode = 'REALTIME'
    </select>

    <select id="countByModelLayer" resultType="java.lang.Long">
        select count(1)
        from data_model
        where layer = #{layer}
    </select>
    <select id="selectCountCreateTableStatus" resultType="java.lang.Long">
        select count(1)
        from data_model dm
        where layer = #{layer}
          and dm.id in (select data_model_id from data_storage);
    </select>
    <select id="selectByLayer" resultType="com.trs.moye.base.data.model.entity.DataModel">
        select *
        from data_model dm
        where layer = #{layer}
    </select>

    <select id="selectLayerById" resultType="com.trs.moye.base.common.enums.ModelLayer">
        select layer
        from data_model
        where id = #{id}
    </select>

    <select id="getStartedStreamProcessDataModelIds" resultType="integer">
        select id
        from data_model
        where layer = 'DWD'
          and execute_status = 'START'
    </select>

    <select id="getStartedDataModelCount" resultType="int">
        select count(*)
        from data_model
        where execute_status = 'START'
        <choose>
            <when test="idCollection == null or idCollection.size == 0">
                and false
            </when>
            <otherwise>
                and id in
                <foreach close=")" collection="idCollection" item="id" open="(" separator=",">
                    #{id,jdbcType=INTEGER}
                </foreach>
            </otherwise>
        </choose>
    </select>
    <select id="isStartStatus" resultType="boolean">
        select count(*)
        from data_model
        where id = #{id,jdbcType=INTEGER}
          and execute_status = 'START'
    </select>
    <select id="isStopStatus" resultType="boolean">
        select count(*)
        from data_model
        where id = #{id,jdbcType=INTEGER}
          and execute_status != 'START'
    </select>

    <select id="getByIds" resultMap="DataModelMap">
        select *
        from data_model where id in
        <foreach collection="dataModelIds" separator="," item="id" open="(" close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectNameByCategoryName" resultType="java.lang.String">
        select dm.zh_name
        from data_model dm
                 join business_category bc on dm.business_category_id = bc.id
        where bc.zh_name = #{categoryName}
    </select>

    <select id="selectSearchableTables" resultMap="SearchDataModelInfoMap">
        select dm.id          as id,
               bc.zh_name     as categoryName,
               bc.description as categoryDesc,
               dm.en_name     as enName,
               dm.zh_name     as zhName,
               dm.description as description
        from data_model dm
                 left join business_category bc on dm.business_category_id = bc.id
        where (dm.description is not null and dm.description != '')
          and exists(select *
                     from data_storage ds
                     where ds.data_model_id = dm.id
                       and ds.create_table_status = 'SUCCESS')
    </select>

    <select id="selectArrangedCount" resultType="java.util.Map">
        SELECT SUM(CASE WHEN is_arranged = 1 THEN 1 ELSE 0 END)          AS arrangedNum,
               SUM(CASE WHEN is_arranged = 0 THEN 1 ELSE 0 END)          AS unArrangedNum,
               SUM(CASE WHEN execute_status = 'STOP' THEN 1 ELSE 0 END)  AS stopNum,
               SUM(CASE WHEN execute_status = 'START' THEN 1 ELSE 0 END) AS startNum,
               SUM(CASE WHEN execute_status = 'PAUSE' THEN 1 ELSE 0 END) AS pauseNum
        FROM data_model
        WHERE layer = 'DWD';
    </select>

    <select id="selectCreateTableStatusCount" resultType="java.util.Map">
        SELECT SUM(CASE WHEN create_table_status = 'SUCCESS' THEN 1 ELSE 0 END) AS createTableNum,
               SUM(CASE WHEN create_table_status = 'NOT' THEN 1 ELSE 0 END)     AS unCreateTableNum,
               SUM(CASE WHEN create_table_status = 'FAIL' THEN 1 ELSE 0 END)    AS createTableFailNum
        FROM data_model
                 join data_storage ds on data_model.id = ds.data_model_id
        WHERE data_model.layer = 'DWD';
    </select>

    <select id="selectBusinessCategoryCount" resultType="java.util.Map">
        select c.zh_name as `key`, count(1) as `value`
        from data_model d
                 join business_category c on d.business_category_id = c.id
        where d.layer = 'DWD'
        GROUP BY c.zh_name
    </select>

    <select id="selectDwdByCategoryId" resultType="java.lang.Integer">
        select id
        from data_model
        where business_category_id = #{businessCategoryId}
          and layer = 'DWD'
    </select>

    <select id="selectByIdsWithStorage" resultMap="DataModelMap">
        select d.*,
               s.id                  as data_storage_id,
               s.en_name             as data_storage_name,
               s.zh_name             as data_storage_comment,
               s.save_mode           as data_storage_save_mode,
               s.create_table_status as data_storage_status,
               s.field_ids           as data_storage_fieldIds,
               s.connection_id       as data_storage_connection_id
        from data_model d
                 left outer join data_storage s on s.data_model_id = d.id
        where d.id in
        <foreach collection="dataModelIds" index="index" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
    </select>

    <select id="selectCreateTableSuccessCount" resultType="long">
        select count(1) as count
        from data_storage
        where data_model_id in (select id from data_model where layer = #{layer})
          and create_table_status = 'SUCCESS'
    </select>
    <select id="selectByLayerAndCategoryId" resultMap="DataModelMap">
        select dm.*,
               s.id                  as data_storage_id,
               s.en_name             as data_storage_name,
               s.zh_name             as data_storage_comment,
               s.save_mode           as data_storage_save_mode,
               s.create_table_status as data_storage_status,
               s.field_ids           as data_storage_fieldIds,
               s.connection_id       as data_storage_connection_id,
               s.settings            as data_storage_settings
        from data_model dm left join data_storage s on dm.id = s.data_model_id
        where business_category_id = #{categoryId}
          and layer = #{layer}
    </select>

    <select id="selectIdsByLayer" resultType="java.lang.Integer">
        select id
        from data_model
        where layer = #{layer}
    </select>

    <select id="selectByLayerAndCategoryName" resultMap="DataModelMap">
        select dm.*,
               s.id                  as data_storage_id,
               s.en_name             as data_storage_name,
               s.zh_name             as data_storage_comment,
               s.save_mode           as data_storage_save_mode,
               s.create_table_status as data_storage_status,
               s.field_ids           as data_storage_fieldIds,
               s.connection_id       as data_storage_connection_id,
               s.settings            as data_storage_settings
        from data_model dm
        left join data_storage s on dm.id = s.data_model_id
        left join business_category bc on dm.business_category_id = bc.id
        where bc.zh_name = #{category}
          and dm.layer = #{layer}
    </select>

    <select id="getByEnNameAndConnectionId" resultMap="DataModelMap">
        select *
        from data_model
        where en_name = #{enName}
          and exists(select *
                     from data_storage ds
                     where ds.data_model_id = data_model.id
                       and ds.connection_id = #{connectionId})
        limit 1
    </select>
    <select id="listByThemeModelSources" resultMap="BaseResultMap">
        select id
        from data_model
        where layer = #{layer}
    </select>
</mapper>