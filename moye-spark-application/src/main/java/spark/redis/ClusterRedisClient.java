package spark.redis;

import redis.clients.jedis.JedisCluster;

/**
 * ClusterRedisClient
 *
 * <AUTHOR>
 * @since 2025/4/29 11:32
 */
public class ClusterRedisClient implements RedisClient {

    private final JedisCluster jedisCluster;

    public ClusterRedisClient(JedisCluster jedisCluster) {
        this.jedisCluster = jedisCluster;
    }

    @Override
    public String getField(String key, String field) {
        return jedisCluster.hget(key, field);
    }

    @Override
    public Long pushElement(String key, String value) {
        Long result = jedisCluster.rpush(key, value);
        // 设置过期时间为1天
        jedisCluster.pexpire(key, 86400000L);
        return result;
    }

    @Override
    public void publish(String channel, String message) {
        jedisCluster.publish(channel, message);
    }

    @Override
    public void close() {
        if (jedisCluster != null) {
            jedisCluster.close();
        }
    }
}
