package com.trs.ai.moye.data.model.request;

import com.trs.ai.moye.data.model.enums.ArrangeDisplayType;
import com.trs.ai.moye.data.model.enums.ProcessType;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

/**
 * 保存数据治理相关信息请求
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/12/20 15:11:22
 */
@Data
@Validated
public class SaveArrangeInfoRequest {

    /**
     * 数据治理方式
     */
    @NotNull(message = "数据治理方式不能为空")
    private ProcessType processType;

    /**
     * 处理模式
     */
    private ArrangeDisplayType processingMode;

}
