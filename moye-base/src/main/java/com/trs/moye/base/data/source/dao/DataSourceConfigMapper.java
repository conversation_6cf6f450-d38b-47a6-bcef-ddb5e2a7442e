package com.trs.moye.base.data.source.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.moye.base.data.model.entity.DataSourceConfig;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 数据源mapper
 *
 * <AUTHOR>
 * @since 2024/9/12 16:30
 */
@Mapper
public interface DataSourceConfigMapper extends BaseMapper<DataSourceConfig> {

    /**
     * 获取该对象被连接额次数
     *
     * @param connectionId 连接ID
     * @return {@link DataSourceConfig}
     * <AUTHOR>
     * @since 2024/9/12 16:57
     */
    List<DataSourceConfig> selectByConnectionId(@Param("connectionId") Integer connectionId);

    /**
     * 根据主键查询
     *
     * @param id 主键
     * @return {@link  DataSourceConfig}
     */
    DataSourceConfig selectById(@Param("id") Integer id);

    /**
     * 根据建模主键查询单条数据来源（贴源库、流处理要素库来源只有1个）
     *
     * @param dataModelId 数据模型ID
     * @return {@link  DataSourceConfig}
     */
    DataSourceConfig selectOneByDataModelId(@Param("dataModelId") Integer dataModelId);

    /**
     * 根据建模主键查询
     *
     * @param dataModelId 数据模型ID
     * @return {@link  DataSourceConfig}
     */
    List<DataSourceConfig> selectByDataModel(@Param("dataModelId") Integer dataModelId);

    /**
     * 根据建模主键查询
     *
     * @param dataModelId 数据模型ID
     * @return {@link  DataSourceConfig}
     */
    int deleteByDataModelId(@Param("dataModelId") Integer dataModelId);


    /**
     * 通过dataModelIds查询
     *
     * @param sourceModelIds 数据建模id
     * @return {@link DataSourceConfig}
     * <AUTHOR>
     * @since 2024/10/16 15:40
     */
    List<DataSourceConfig> selectByDataModelIds(@Param("sourceModelIds") List<Integer> sourceModelIds);

    /**
     * 查询依赖指定建模id的数据源配置
     *
     * @param sourceModelId 数据建模id
     * @return {@link DataSourceConfig}
     */
    List<DataSourceConfig> selectBySourceModelId(@Param("sourceModelId") Integer sourceModelId);

    /**
     * 批量更新数据源配置 根据id批量更新指定字段的值
     *
     * @param dataSourceConfigs 数据源配置列表
     * @return 更新的记录数
     */
    int updateBatch(@Param("dataSourceConfigs") List<DataSourceConfig> dataSourceConfigs);

}