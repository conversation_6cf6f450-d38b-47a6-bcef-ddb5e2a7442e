package com.trs.moye.ability.entity;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.trs.moye.ability.entity.Schema.ObjectTypeSchema;
import com.trs.moye.ability.enums.AbilityFieldBindType;
import com.trs.moye.ability.utils.ValueParseUtils;
import com.trs.moye.base.common.utils.JsonUtils;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * 数据绑定工具 - 用于定义 JSON 字段与方法参数的绑定关系
 */
@Getter
@NoArgsConstructor
public class InputBind implements Serializable {

    private final Map<String, Binding> bindings = new LinkedHashMap<>();


    public InputBind(Map<String, Binding> bindings) {
        this.bindings.putAll(bindings);
    }

    /**
     * 添加路径绑定规则
     *
     * @param paramName 方法参数名
     * @param jsonPath  JSON 路径
     * @return 当前对象
     */
    public InputBind addBinding(String paramName, String jsonPath) {
        bindings.put(paramName, new PropertyBinding(jsonPath));
        return this;
    }

    /**
     * 添加固定值绑定规则
     *
     * @param paramName  方法参数名
     * @param fixedValue 固定值
     * @return 当前对象
     */
    public InputBind addFixedValueBinding(String paramName, Object fixedValue) {
        bindings.put(paramName, new FixedValueBinding(fixedValue));
        return this;
    }

    /**
     * 添加数组拼接绑定规则
     *
     * @param paramName 方法参数名
     * @param jsonPaths JSON 路径列表
     * @return 当前对象
     */
    public InputBind addArrayBinding(String paramName, List<String> jsonPaths) {
        bindings.put(paramName, new ArrayBinding(jsonPaths));
        return this;
    }

    /**
     * 获取绑定信息
     *
     * @param paramName 方法参数名
     * @return 绑定信息
     */
    public Binding getBinding(String paramName) {
        return bindings.get(paramName);
    }

    /**
     * 转换为 JSON 字符串
     *
     * @return JSON 字符串
     */
    public String toString() {
        return JsonUtils.toJsonString(bindings);
    }

    /**
     * 绑定信息封装类
     */
    @AllArgsConstructor
    @NoArgsConstructor
    @Getter
    public abstract static class Binding implements Serializable {

        /**
         * 排序
         */
        Integer order;

        /**
         * 解析 JSON 字段值
         *
         * @param valueNode JSON 字段值
         * @param type      目标类型
         * @return 解析后的值
         * @throws IllegalArgumentException 如果解析失败
         */
        public abstract Object parseValue(JsonNode valueNode, Class<?> type) throws IllegalArgumentException;

        /**
         * 声明绑定类型: VALUE or PROPERTY
         *
         * @return 绑定类型 {@link AbilityFieldBindType}
         */
        public abstract AbilityFieldBindType getBindType();
    }

    /**
     * 路径绑定
     */
    @NoArgsConstructor
    @Getter
    public static class PropertyBinding extends Binding {

        String jsonPath;

        public PropertyBinding(String jsonPath) {
            this.jsonPath = jsonPath;
        }

        public PropertyBinding(String jsonPath, Integer order) {
            super(order);
            this.jsonPath = jsonPath;
        }

        @Override
        public Object parseValue(JsonNode input, Class<?> type) {
            JsonNode valueNode = ValueParseUtils.getSourceValueFromJsonPath(input, jsonPath);
            // 路径绑定
            if (valueNode.isMissingNode()) {
                throw new IllegalArgumentException("No value found at path: " + jsonPath);
            }

            // 特殊处理：当目标类型是字符串，但源值是数组或对象时，将整个节点转为字符串
            if (type == String.class && (valueNode.isArray() || valueNode.isObject())) {
                return valueNode.toString();
            }

            // 如果路径指向数组，且目标类型也是数组，需要进行适当转换
            if (valueNode.isArray() && type.isArray()) {
                List<Object> valuesList = new ArrayList<>();
                valueNode.forEach(node -> valuesList.add(JsonUtils.treeToValue(node, Object.class)));
                return convertToArray(valuesList, type.getComponentType());
            }

            return JsonUtils.treeToValue(valueNode, type);
        }

        /**
         * 声明绑定类型: PROPERTY
         *
         * @return 绑定类型 {@link AbilityFieldBindType}
         */
        @Override
        public AbilityFieldBindType getBindType() {
            return AbilityFieldBindType.PROPERTY;
        }
    }

    /**
     * 固定值绑定
     */
    @NoArgsConstructor
    @Getter
    public static class FixedValueBinding extends Binding {

        Object fixedValue;

        public FixedValueBinding(Object fixedValue) {
            this.fixedValue = fixedValue;
        }

        public FixedValueBinding(Object fixedValue, Integer order) {
            super(order);
            this.fixedValue = fixedValue;
        }

        @Override
        public Object parseValue(JsonNode valueNode, Class<?> type) {
            // 如果固定值为null，直接返回null
            if (fixedValue == null) {
                return null;
            }
            // 如果固定值已经是目标类型，直接返回
            if (type.isInstance(fixedValue)) {
                return fixedValue;
            }
            // 对于基本类型的处理
            if (type.isPrimitive()) {
                return fixedValue;
            }
            // 使用JsonUtils进行复杂类型转换
            try {
                return JsonUtils.convertValue(fixedValue, type);
            } catch (Exception e) {
                throw new IllegalArgumentException(
                    "Cannot convert fixed value of type " + fixedValue.getClass().getName() + " to required type "
                        + type.getName(), e);
            }
        }

        @Override
        public AbilityFieldBindType getBindType() {
            return AbilityFieldBindType.VALUE;
        }
    }

    /**
     * 数组拼接绑定
     */
    @NoArgsConstructor
    @Getter
    public static class ArrayBinding extends Binding {

        private List<String> jsonPaths;

        public ArrayBinding(List<String> jsonPaths) {
            this.jsonPaths = jsonPaths;
        }

        public ArrayBinding(List<String> jsonPaths, Integer order) {
            super(order);
            this.jsonPaths = jsonPaths;
        }

        @Override
        public Object parseValue(JsonNode input, Class<?> type) {
            // 从所有指定路径收集值
            List<Object> valuesList = collectValuesFromPaths(input);
            // 如果目标类型是数组，进行转换（例如：String[]）
            if (type.isArray()) {
                return convertToArray(valuesList, type.getComponentType());
            }
            // 对于非数组类型，直接返回列表
            return valuesList;
        }

        /**
         * 从指定的JSON路径中收集值
         *
         * @param input 输入
         * @return {@link List }<{@link Object }>
         */
        private List<Object> collectValuesFromPaths(JsonNode input) {
            return jsonPaths.stream().map(jsonPath -> {
                JsonNode valueNode = ValueParseUtils.getSourceValueFromJsonPath(input, jsonPath);
                if (valueNode.isMissingNode()) {
                    throw new IllegalArgumentException("No value found at path: " + jsonPath);
                }
                return JsonUtils.treeToValue(valueNode, Object.class);
            }).collect(Collectors.toList());
        }

        /**
         * 数组拼接绑定, 声明绑定类型: PROPERTY
         *
         * @return {@link AbilityFieldBindType }
         */
        @Override
        public AbilityFieldBindType getBindType() {
            return AbilityFieldBindType.PROPERTY;
        }
    }

    /**
     * 对象数组绑定
     */
    @NoArgsConstructor
    @Getter
    public static class ObjectArrayBinding extends Binding {

        private List<InputBind> items;


        public ObjectArrayBinding(List<InputBind> items) {
            this.order = 0;
            this.items = items;
        }

        public ObjectArrayBinding(List<InputBind> items, Integer order) {
            this.items = items;
            this.order = order;
        }

        @Override
        public Object parseValue(JsonNode valueNode, Class<?> type) throws IllegalArgumentException {
            List<Map<String, Object>> valuesList = new ArrayList<>();
            for (InputBind item : this.items) {
                // 解析每个绑定项的值
                Map<String, Object> itemValues = new HashMap<>();
                for (Entry<String, Binding> entry : item.getBindings().entrySet()) {
                    Binding itemBinding = entry.getValue();
                    Object itemValue = itemBinding.parseValue(valueNode, Object.class);
                    if (itemValue != null) {
                        itemValues.put(entry.getKey(), itemValue);
                    }
                }
                valuesList.add(itemValues);
            }
            return valuesList;
        }

        @Override
        public AbilityFieldBindType getBindType() {
            return null;
        }
    }

    /**
     * 对象数组绑定
     */
    @NoArgsConstructor
    @Getter
    public static class ObjectBinding extends Binding {

        private Map<String, Binding> properties;


        public ObjectBinding(Map<String, Binding> properties) {
            this.order = 0;
            this.properties = properties;
        }

        public ObjectBinding(Map<String, Binding> properties, Integer order) {
            this.properties = properties;
            this.order = order;
        }

        @Override
        public Object parseValue(JsonNode valueNode, Class<?> type) throws IllegalArgumentException {
            Map<String, Object> itemValues = new HashMap<>();
            for (Entry<String, Binding> entry : properties.entrySet()) {
                Binding itemBinding = entry.getValue();
                Object itemValue = itemBinding.parseValue(valueNode, Object.class);
                if (itemValue != null) {
                    itemValues.put(entry.getKey(), itemValue);
                }
            }
            return itemValues;
        }

        @Override
        public AbilityFieldBindType getBindType() {
            return null;
        }
    }

    /**
     * 将值列表转换为指定类型的数组
     *
     * @param valuesList    值列表
     * @param componentType 组件类型
     * @return {@link Object }
     */
    private static Object convertToArray(List<Object> valuesList, Class<?> componentType) {
        // 字符串数组的特殊处理
        if (componentType == String.class) {
            return convertToStringArray(valuesList);
        }
        // 其他类型数组的处理
        return convertToGenericArray(valuesList, componentType);
    }

    /**
     * 将值列表转换为字符串数组
     *
     * @param valuesList 值列表
     * @return {@link String} 字符串数组
     */
    private static String[] convertToStringArray(List<Object> valuesList) {
        return valuesList.stream().map(obj -> obj != null ? obj.toString() : null).toArray(String[]::new);
    }

    /**
     * 将值列表转换为指定类型的泛型数组
     *
     * @param valuesList    值列表
     * @param componentType 组件类型
     * @return {@link Object }
     */
    private static Object convertToGenericArray(List<Object> valuesList, Class<?> componentType) {
        Object array = java.lang.reflect.Array.newInstance(componentType, valuesList.size());
        for (int i = 0; i < valuesList.size(); i++) {
            Object value = valuesList.get(i);
            if (value == null) {
                continue;
            }
            try {
                java.lang.reflect.Array.set(array, i, value);
            } catch (IllegalArgumentException e) {
                throw new IllegalArgumentException("无法将 " + value.getClass() + " 转换为 " + componentType.getName(),
                    e);
            }
        }
        return array;
    }

    /**
     * 从 JSON 节点创建 InputBind 对象
     *
     * @param json JSON 节点
     * @return InputBind 对象
     */
    public static InputBind fromJson(JsonNode json) {

        // 遍历 JSON 节点
        JsonNode bindingsNode = json.has("bindings") ? json.get("bindings") : json;

        Map<String, JsonNode> bindingsMap = new HashMap<>();
        bindingsNode.fields().forEachRemaining(field -> {
            JsonNode bindingNode = field.getValue();
            String fieldName = bindingNode.has("fieldName") ? bindingNode.get("fieldName").asText() : field.getKey();
            Integer order = bindingNode.has("order") ? bindingNode.get("order").asInt() : 0;
            bindingsMap.put(fieldName, bindingNode);
        });

        // 使用LinkedHashMap保持顺序
        Map<String, Binding> orderedBindings = parseBinding(bindingsMap);

        // 替换原map
        InputBind inputBind = new InputBind();
        inputBind.bindings.clear();
        inputBind.bindings.putAll(orderedBindings);
        return inputBind;
    }

    private static Map<String, Binding> parseBinding(Map<String, JsonNode> bindingsMap) {
        // order默认为0
        Integer order = 0;
        Map<String, Binding> bindings = new LinkedHashMap<>();
        for (Map.Entry<String, JsonNode> entry : bindingsMap.entrySet()) {
            String fieldName = entry.getKey();
            JsonNode bindingNode = entry.getValue();
            String[] splitName = fieldName.split("\\.");
            if (splitName.length == 1) {
                // 解析绑定逻辑
                Binding binding = parseBindingNode(bindingNode, order);
                bindings.put(fieldName, binding);
            } else if (splitName.length >= 2) {
                // 字段名中间有.的，是对象类型的老数据兼容，分组绑定逻辑
                String groupName = splitName[0];
                // 获取所有以groupName开头的字段
                Map<String, JsonNode> innerBindingsMap = new LinkedHashMap<>();
                for (Map.Entry<String, JsonNode> innerEntry : bindingsMap.entrySet()) {
                    String innerFieldName = innerEntry.getKey();
                    if (innerFieldName.startsWith(groupName + ".")) {
                        String innerSubFieldName = innerFieldName.replace(groupName + ".", "");
                        innerBindingsMap.put(innerSubFieldName, innerEntry.getValue());
                    }
                }
                Map<String, Binding> innerBindings = parseBinding(innerBindingsMap);
                ObjectBinding objectBinding = new ObjectBinding(innerBindings, order);
                bindings.put(groupName, objectBinding);
            } else {
                throw new IllegalArgumentException("字段名格式错误: " + fieldName);
            }
        }
        return bindings;
    }

    private static Binding parseBindingNode(JsonNode bindingNode, Integer order) {
        if (bindingNode.has("jsonPath")) {
            // 路径绑定
            String jsonPath = bindingNode.get("jsonPath").asText();
            return new PropertyBinding(jsonPath, order);
        } else if (bindingNode.has("fixedValue")) {
            // 固定值绑定
            return getBindingFromFixedValue(bindingNode.get("fixedValue"), order);
        } else if (bindingNode.has("jsonPaths")) {
            // 数组绑定
            List<String> jsonPaths = new ArrayList<>();
            for (JsonNode pathNode : bindingNode.get("jsonPaths")) {
                jsonPaths.add(pathNode.asText());
            }
            return new ArrayBinding(jsonPaths, order);
        } else if (bindingNode.has("items") && bindingNode.get("items").isArray()) {
            // 对象数组绑定
            ArrayNode itemsNode = (ArrayNode) bindingNode.get("items");
            List<InputBind> items = new ArrayList<>();
            for (JsonNode itemNode : itemsNode) {
                items.add(fromJson(itemNode));
            }
            return new ObjectArrayBinding(items, order);
        } else if (bindingNode.has("properties") && bindingNode.get("properties").isObject()) {
            // 对象绑定
            ObjectNode propertiesNode = (ObjectNode) bindingNode.get("properties");
            Map<String, Binding> properties = new LinkedHashMap<>();
            propertiesNode.fields().forEachRemaining(entry -> {
                String propertyName = entry.getKey();
                JsonNode propertyValue = entry.getValue();
                Binding propertyBinding = parseBindingNode(propertyValue, order);
                properties.put(propertyName, propertyBinding);
            });
            return new ObjectBinding(properties, order);
        } else {
            throw new IllegalArgumentException("Unknown binding type for parameter: " + bindingNode);
        }
    }

    private static Binding getBindingFromFixedValue(JsonNode fixedValue, Integer order) {
        // 老数据兼容：
        // 如果fixedValue绑定的是对象数组类型，则返回ObjectArrayBinding，绑定方式均为fixedValue
        if (fixedValue.isArray() && fixedValue.size() > 0 && fixedValue.get(0).isObject()) {
            List<InputBind> items = new ArrayList<>();
            ArrayNode itemsValue = (ArrayNode) fixedValue;
            for (JsonNode item : itemsValue) {
                Map<String, Binding> bindings = new LinkedHashMap<>();
                item.fields().forEachRemaining(itemField -> {
                    String fieldName = itemField.getKey();
                    JsonNode value = itemField.getValue();
                    Binding itemBinding = new FixedValueBinding(value, 0);
                    bindings.put(fieldName, itemBinding);
                });
                items.add(new InputBind(bindings));
            }
            return new ObjectArrayBinding(items, order);
        } else {
            Object value = JsonUtils.treeToValue(fixedValue, Object.class);
            return new FixedValueBinding(value, order);
        }
    }

    /**
     * 从输入参数绑定关系中获取参数字段英文名和参数值
     *
     * @param message     输入消息内容
     * @param inputSchema 算子输入参数
     * @return 参数
     */
    public Map<String, Object> getParamsMap(JsonNode message, Schema inputSchema) {
        Map<String, Object> map = new HashMap<>(bindings.size());
        this.bindings.forEach((fieldName, binding) -> map.put(createDisplayFieldName(fieldName, binding, inputSchema),
            binding.parseValue(message, Object.class)));
        return map;
    }

    private String createDisplayFieldName(String fieldName, Binding binding, Schema inputSchema) {
        String abilityParamName = createAbilityParamName(fieldName, inputSchema);
        String path = createBindingPath(binding);
        return StringUtils.isNotEmpty(path) ? abilityParamName + " (来源字段:" + path + ")" : abilityParamName;
    }

    private String createAbilityParamName(String fieldName, Schema inputSchema) {
        if (inputSchema instanceof ObjectTypeSchema) {
            ObjectTypeSchema objectTypeSchema = (ObjectTypeSchema) inputSchema;
            if (objectTypeSchema.getProperties() != null && objectTypeSchema.getProperties().containsKey(fieldName)) {
                return objectTypeSchema.getProperties().get(fieldName).getZhName() + " (" + fieldName + ")";
            }
        }
        return fieldName;
    }

    private String createBindingPath(Binding binding) {
        if (binding instanceof PropertyBinding) {
            PropertyBinding propertyBinding = (PropertyBinding) binding;
            return propertyBinding.getJsonPath();
        } else if (binding instanceof ArrayBinding) {
            ArrayBinding arrayBinding = (ArrayBinding) binding;
            return String.join(";", arrayBinding.getJsonPaths());
        }
        return null;
    }

    /**
     * 从显示格式的字段名映射中还原回原始参数映射
     *
     * @param displayMap 由getParamsMap生成的显示格式字段映射
     * @return 还原后的原始参数映射
     */
    public static Map<String, Object> restoreOriginalParams(Map<String, Object> displayMap) {
        Map<String, Object> originalMap = new HashMap<>(displayMap.size());

        for (Map.Entry<String, Object> entry : displayMap.entrySet()) {
            processDisplayMapEntry(entry, originalMap);
        }

        return originalMap;
    }

    /**
     * 处理显示映射中的单个条目
     *
     * @param entry       显示映射条目
     * @param originalMap 存储还原后参数的映射
     */
    private static void processDisplayMapEntry(Map.Entry<String, Object> entry, Map<String, Object> originalMap) {
        String displayFieldName = entry.getKey();
        Object value = entry.getValue();

        PathInfo pathInfo = extractPathInfo(displayFieldName);

        if (pathInfo == null) {
            // 无路径信息的字段，直接添加
            originalMap.put(displayFieldName, value);
            return;
        }

        if (pathInfo.path.contains(";")) {
            // ArrayBinding情况
            processArrayBinding(pathInfo.path, value, originalMap);
        } else {
            // PropertyBinding情况
            processPropertyBinding(pathInfo, value, originalMap);
        }
    }

    /**
     * 从显示字段名提取路径信息
     *
     * @param displayFieldName 显示字段名
     * @return 路径信息，如果没有则返回null
     */
    private static PathInfo extractPathInfo(String displayFieldName) {
        int pathStartIndex = displayFieldName.lastIndexOf("(");
        int pathEndIndex = displayFieldName.lastIndexOf(")");

        if (pathStartIndex > 0 && pathEndIndex > pathStartIndex) {
            String path = displayFieldName.substring(pathStartIndex + 1, pathEndIndex);
            String fieldNamePart = displayFieldName.substring(0, pathStartIndex);
            return new PathInfo(path, fieldNamePart);
        }

        return null;
    }

    /**
     * 处理数组绑定
     *
     * @param pathInfo    路径信息
     * @param value       值对象
     * @param originalMap 存储还原后参数的映射
     */
    private static void processArrayBinding(String pathInfo, Object value, Map<String, Object> originalMap) {
        if (!(value instanceof List)) {
            return;
        }

        List<?> valueList = (List<?>) value;
        String[] paths = pathInfo.split(";");

        for (int i = 0; i < paths.length && i < valueList.size(); i++) {
            String path = paths[i];
            Object item = valueList.get(i);

            String fieldName = extractFieldNameFromPath(path);
            if (fieldName != null) {
                // 如果值为null，将其替换为空字符串
                originalMap.put(fieldName, item == null ? "" : item);
            }

        }
    }

    /**
     * 处理属性绑定
     *
     * @param pathInfo    路径信息
     * @param value       值对象
     * @param originalMap 存储还原后参数的映射
     */
    private static void processPropertyBinding(PathInfo pathInfo, Object value, Map<String, Object> originalMap) {
        String fieldName = extractFieldNameFromPath(pathInfo.path);

        if (fieldName != null) {
            originalMap.put(fieldName, value);
        } else {
            // 如果无法从路径提取字段名，使用原始字段名
            originalMap.put(pathInfo.fieldNamePart, value);
        }
    }

    /**
     * 路径信息数据结构
     */
    private static class PathInfo {

        final String path;
        final String fieldNamePart;

        PathInfo(String path, String fieldNamePart) {
            this.path = path;
            this.fieldNamePart = fieldNamePart;
        }
    }

    /**
     * 从路径中提取字段名
     *
     * @param path JSON路径
     * @return 提取的字段名
     */
    private static String extractFieldNameFromPath(String path) {
        // 处理以'/'开头的路径，提取最后一个路径段作为字段名
        if (path.startsWith("/")) {
            String[] segments = path.split("/");
            // 获取最后一个非空路径段
            for (int i = segments.length - 1; i >= 0; i--) {
                if (!segments[i].isEmpty()) {
                    return segments[i];
                }
            }
        }
        return null;
    }

    /**
     * 获取子字段绑定配置
     *
     * @param paramName 父参数名（如"user"）
     * @return 子字段绑定映射（如"name" -> Binding）
     */
    public Map<String, Binding> getSubFieldBindings(String paramName) {
        String prefix = paramName + ".";
        return bindings.entrySet().stream().filter(entry -> entry.getKey().startsWith(prefix))
            .collect(Collectors.toMap(entry -> entry.getKey().substring(prefix.length()), Entry::getValue));
    }

    /**
     * 判断是否存在子字段绑定
     *
     * @param paramName 父参数名（如"user"）
     * @return 存在子字段绑定时返回true
     */
    public boolean hasSubFieldBindings(String paramName) {
        String prefix = paramName + ".";
        return bindings.keySet().stream().anyMatch(key -> key.startsWith(prefix));
    }

}