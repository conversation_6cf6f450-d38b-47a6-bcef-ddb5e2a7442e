package com.trs.moye.batch.engine.service;

import static com.trs.moye.batch.engine.constants.RedisKeyConstants.TASK_EXECUTION_RECORD_CHANNEL;

import com.trs.ai.moye.redis.starter.service.RedisService;
import com.trs.moye.ability.entity.operator.BatchOperatorExecutionRecord;
import com.trs.moye.ability.entity.operator.BatchOperatorRuntimeEntity;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.batch.engine.constants.RedisKeyConstants;
import com.trs.moye.batch.engine.entity.BatchTaskTracer;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.stereotype.Service;

/**
 * 负责 moye批处理任务redis相关 的 读写
 */
@Slf4j
@Service
public class BatchTaskRedisService {


    @Resource
    private RedisService redisService;
    @Resource
    private RuntimeOperatorService runtimeOperatorService;
    @Resource
    private RedisMessageListenerContainer redisMessageListenerContainer;


    /**
     * 初始化, 订阅算子执行情况更新消息, 更新消息
     */
    @EventListener(ApplicationReadyEvent.class)
    public void init() {
        // 订阅算子执行情况更新消息, 更新消息
        MessageListener listener = (message, pattern) -> {
            String channel = new String(message.getChannel());
            String body = new String(message.getBody());
            log.info("收到redis消息 - 频道: {}, 内容: {}", channel, body);

            // 解析消息格式 key::length
            String[] parts = body.split("::");
            if (parts.length == 2) {
                String redisKey = parts[0];
                long length = Long.parseLong(parts[1]);
                // 去掉前缀获取executeId
                String executeId = redisKey.replace(RedisKeyConstants.TASK_EXECUTION_RECORD, "");

                // 获取最新元素
                Object latestItem = redisService.getValueFromList(redisKey, length - 1);
                log.info("获取到最新元素: {}", latestItem);

                // 解析最新元素, 存储到ck
                BatchOperatorExecutionRecord operatorExecutionRecord = JsonUtils.parseObject(
                    JsonUtils.toJsonString(latestItem),
                    BatchOperatorExecutionRecord.class);
                if (Objects.nonNull(operatorExecutionRecord)) {
                    BatchTaskMonitor.insertTracer(
                        BatchTaskTracer.fromOperatorExecutionRecord(executeId, operatorExecutionRecord));
                }
            } else {
                log.error("redis消息格式错误, 消息: {}", body);
            }
        };

        redisMessageListenerContainer.addMessageListener(listener, new ChannelTopic(TASK_EXECUTION_RECORD_CHANNEL));
        log.info("成功订阅redis频道: {}", TASK_EXECUTION_RECORD_CHANNEL);
    }


    /**
     * 更新算子执行情况到redis
     *
     * @param dataModelId 数据模型id
     * @param taskName    任务名称
     * @param beginTime   开始时间
     * @param endTime     结束时间
     */
    public void updateOperatorToRedis(Integer dataModelId, String taskName, LocalDateTime beginTime,
        LocalDateTime endTime) {
        List<BatchOperatorRuntimeEntity> operators = runtimeOperatorService.createRuntimeOperators(dataModelId,
            beginTime, endTime);
        updateTaskToRedis(dataModelId, taskName, operators);
    }

    private void updateTaskToRedis(Integer taskId, String taskName, List<BatchOperatorRuntimeEntity> executeInfoList) {
        //存redis
        //key: moye-batch-engine:task:{taskId} value: @TaskInfo
        Map<String, Object> taskInfoMap = new HashMap<>(3);
        taskInfoMap.put("taskId", taskId);
        taskInfoMap.put("taskName", taskName);
        taskInfoMap.put("executeInfo", executeInfoList);
        redisService.putMapIntoHash(RedisKeyConstants.BATCH_TASK + taskId, taskInfoMap);
        log.info("更新配置到redis！taskId:{} info:{}", taskId, JsonUtils.toJsonString(executeInfoList));
    }

}
