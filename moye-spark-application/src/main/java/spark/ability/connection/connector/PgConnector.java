package spark.ability.connection.connector;

import com.trs.moye.ability.entity.operator.BatchOperatorRuntimeEntity.TableInfo;
import com.trs.moye.base.common.utils.conditions.SqlUtils;
import com.trs.moye.base.data.connection.entity.params.PostgresqlConnectionParams;
import com.trs.moye.base.data.model.enums.DataSaveMode;
import com.trs.moye.base.data.service.entity.Condition;
import java.util.List;
import java.util.Objects;
import java.util.Properties;
import lombok.extern.slf4j.Slf4j;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SaveMode;
import org.apache.spark.sql.SparkSession;

/**
 * postgresql
 *
 * <AUTHOR>
 * @since 2024/10/31 10:13
 */
@Slf4j
public class PgConnector implements Connector {

    private String getSchemaDotTable(String schema, String table) {
        return String.format("\"%s\".\"%s\"", schema, table);
    }

    @Override
    public Dataset<Row> getInputDf(SparkSession sparkSession, TableInfo storageParams, List<Condition> conditions, String sql) {
        if (storageParams.getConnectionParams() instanceof PostgresqlConnectionParams) {
            PostgresqlConnectionParams pgParams = (PostgresqlConnectionParams) storageParams.getConnectionParams();
            String schema = pgParams.getSchema();
            String table = storageParams.getDbTable();

            String subquery = getSchemaDotTable(schema, table);
            if (Objects.nonNull(conditions) && !conditions.isEmpty()) {
                String querySql = SqlUtils.getQuerySql(conditions, pgParams.getConnectionType(), schema, table);
                log.info("postgresql: {}", querySql);
                subquery = String.format("(%s) as subquery", querySql);
            }
            storageParams.setSubquery(subquery);
            return sparkSession.read().format("jdbc")
                .option("url", pgParams.getJdbcUrl(null))
                .option("driver", pgParams.getJdbcDriverName(null))
                .option("dbtable", subquery)
                .option("user", pgParams.getUsername())
                .option("password", pgParams.getDecryptedPassword())
                .load();
        } else {
            throw new IllegalArgumentException("mysql connect param type wrong!");
        }
    }

    @Override
    public void save(SparkSession sparkSession, Dataset<Row> df, TableInfo storageParams) {
        if (storageParams.getConnectionParams() instanceof PostgresqlConnectionParams) {
            PostgresqlConnectionParams pgParams = (PostgresqlConnectionParams) storageParams.getConnectionParams();
            Properties properties = new Properties();
            properties.put("user", pgParams.getUsername());
            properties.put("password", pgParams.getDecryptedPassword());
            df.write()
                .mode(DataSaveMode.DROP_DATA.equals(storageParams.getSaveMode()) ? SaveMode.Overwrite : SaveMode.Append)
                .jdbc(pgParams.getJdbcUrl(null), getSchemaDotTable(pgParams.getSchema(), storageParams.getDbTable()), properties);
        } else {
            throw new IllegalArgumentException("mysql connect param type wrong!");
        }

    }
}
