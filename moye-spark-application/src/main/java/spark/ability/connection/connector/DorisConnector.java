package spark.ability.connection.connector;

import com.trs.moye.ability.entity.operator.BatchOperatorRuntimeEntity.TableInfo;
import com.trs.moye.base.common.utils.conditions.SqlUtils;
import com.trs.moye.base.data.connection.entity.params.DorisConnectionParams;
import com.trs.moye.base.data.model.enums.DataSaveMode;
import com.trs.moye.base.data.service.entity.Condition;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SaveMode;
import org.apache.spark.sql.SparkSession;

/**
 * doris
 *
 * <AUTHOR>
 * @since 2025/05/08 10:30
 */
@Slf4j
public class DorisConnector implements Connector {

    private String getSchemaDotTable(String schema, String table) {
        return String.format("%s.%s", schema, table);
    }

    private String getFenodes(String host, Integer port) {
        return String.format("%s:%s", host, port);
    }

    @Override
    public Dataset<Row> getInputDf(SparkSession sparkSession, TableInfo storageParams, List<Condition> conditions, String sql) {
        if (storageParams.getConnectionParams() instanceof DorisConnectionParams) {
            DorisConnectionParams params = (DorisConnectionParams) storageParams.getConnectionParams();
            String table = storageParams.getDbTable();

            if ("connector".equals(storageParams.getDorisAccessMode())) {
                return readByConnector(sparkSession, conditions, params, table);
            }
            return readByJdbc(sparkSession, conditions, params, table);

        } else {
            throw new IllegalArgumentException("doris connect param type wrong!");
        }
    }

    private Dataset<Row> readByConnector(SparkSession sparkSession, List<Condition> conditions,
        DorisConnectionParams params, String table) {
        if (Objects.nonNull(conditions) && !conditions.isEmpty()) {
            String tempView = "spark_doris_" + table;
            String querySql = SqlUtils.getQuerySql(conditions, params.getConnectionType(), tempView);
            sparkSession.sql("CREATE TEMPORARY VIEW " + tempView + " " +
                             "USING doris " +
                             "OPTIONS( " +
                             "  \"table.identifier\"=\"" + getSchemaDotTable(params.getDatabase(), table) + "\", " +
                             "  \"fenodes\"=\"" + getFenodes(params.getHost(), params.getPort()) + "\", " +
                             "  \"user\"=\"" + params.getUsername() + "\", " +
                             "  \"password\"=\"" + params.getDecryptedPassword() + "\" " +
                             ")");
            return sparkSession.sql(querySql);
        }
        return sparkSession.read().format("doris")
            .option("doris.table.identifier", getSchemaDotTable(params.getDatabase(), table))
            .option("doris.fenodes", getFenodes(params.getHost(), params.getPort()))
            .option("user", params.getUsername())
            .option("password", params.getDecryptedPassword())
            .load();
    }

    private static Dataset<Row> readByJdbc(SparkSession sparkSession, List<Condition> conditions,
        DorisConnectionParams params, String table) {
        String subquery = table;
        if (Objects.nonNull(conditions) && !conditions.isEmpty()) {
            String querySql = SqlUtils.getQuerySql(conditions, params.getConnectionType(), table);
            subquery = String.format("(%s) as subquery", querySql);
        }
        return sparkSession.read().format("jdbc")
            .option("driver", params.getJdbcDriverName(null))
            .option("url", params.getJdbcUrl(null))
            .option("dbtable", subquery)
            .option("user", params.getUsername())
            .option("password", params.getDecryptedPassword())
            .load();
    }

    @Override
    public void save(SparkSession sparkSession, Dataset<Row> df, TableInfo storageParams) {
        if (storageParams.getConnectionParams() instanceof DorisConnectionParams) {
            DorisConnectionParams params = (DorisConnectionParams) storageParams.getConnectionParams();
            if ("connector".equals(storageParams.getDorisAccessMode())) {
                saveByConnector(df, storageParams, params);
            } else {
                saveByJdbc(df, storageParams, params);
            }
        } else {
            throw new IllegalArgumentException("doris connect param type wrong!");
        }
    }

    private void saveByConnector(Dataset<Row> df, TableInfo storageParams, DorisConnectionParams params) {
        df.write().format("doris")
            .option("doris.table.identifier", getSchemaDotTable(params.getDatabase(), storageParams.getDbTable()))
            .option("doris.fenodes", getFenodes(params.getHost(), params.getPort()))
            .option("user", params.getUsername())
            .option("password", params.getDecryptedPassword())
            //指定要写入的列
            // .option("doris.write.fields", "$YOUR_FIELDS_TO_WRITE")
            .option("doris.sink.properties.format", "arrow")
            .mode(DataSaveMode.DROP_DATA.equals(storageParams.getSaveMode()) ? SaveMode.Overwrite : SaveMode.Append)
            .save();
    }

    private static void saveByJdbc(Dataset<Row> df, TableInfo storageParams, DorisConnectionParams params) {
        df.write()
            .mode(DataSaveMode.DROP_DATA.equals(storageParams.getSaveMode()) ? SaveMode.Overwrite : SaveMode.Append)
            .format("jdbc")
            .option("driver", params.getJdbcDriverName(null))
            .option("url", params.getJdbcUrl(null))
            .option("dbtable", storageParams.getDbTable())
            .option("user", params.getUsername())
            .option("password", params.getDecryptedPassword())
            .save();
    }
}
