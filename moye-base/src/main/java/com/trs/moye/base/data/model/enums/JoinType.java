package com.trs.moye.base.data.model.enums;

import lombok.Getter;

/**
 * 联表的连接关系
 */
@Getter
public enum JoinType {

    LEFT("左外连接"),
    RIGHT("右外连接"),
    ;

    private final String zhName;

    JoinType(String zhName) {
        this.zhName = zhName;
    }

    /**
     * 反向关系，默认无反向关系如inner这类
     */
    private JoinType opposite = this;

    static {
        LEFT.opposite = RIGHT;
        RIGHT.opposite = LEFT;
    }
}
