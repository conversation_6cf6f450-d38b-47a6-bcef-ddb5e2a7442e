package com.trs.ai.moye.common.validation;

import com.trs.ai.moye.data.service.entity.query.Condition;
import com.trs.ai.moye.data.service.enums.DataServiceConditionType;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * 重构后的条件表达式数组校验器，提升可维护性和健壮性
 */
public class ConditionListValidator implements
        ConstraintValidator<ValidCondition, List<Condition>> {

    /**
     * 允许的操作符集合
     */
    private static final Set<String> ALLOWED_OPERATORS = new HashSet<>(Arrays.asList(
            "=", "!=", ">", "<", ">=", "<=",
            "in", "notin", "contain", "notcontain", "like", "notlike", "isnull", "isnotnull",
            "and", "or", "not", "(", ")","regex"
    ));
    /**
     * 不需要值的操作符集合
     */
    private static final Set<String> NO_VALUE_OPERATORS = new HashSet<>(Arrays.asList(
            "isnull", "isnotnull"
    ));

    /**
     * 连续逻辑符号报错信息，带符号占位符
     */
    private static final String MSG_CONSECUTIVE_LOGIC = "不能出现两个连续的逻辑符号\"%s\" 和 \"%s\"。";
    /**
     * 逻辑符号报错信息
     */
    private static final String MSG_MISSING_LOGIC_BETWEEN_EXPR = "表达式之间缺少必要的连接符号，请补充。";
    /**
     * and/or 前后规则报错信息
     */
    private static final String MSG_ANDOR_BEFORE = "“%s”前必须为条件表达式或“）”，请调整表达式顺序。";
    /**
     * and/or 前后规则报错信息
     */
    private static final String MSG_ANDOR_AFTER = "“%s”后必须为条件表达式、“（” 、 “非”。";
    /**
     * and/or 不能在首位或末位报错信息
     */
    private static final String MSG_ANDOR_EDGE = "“%s”不能在首位或末位，请调整表达式顺序。";
    /**
     * 非后面必须跟表达式或左括号报错信息
     */
    private static final String MSG_NOT_FOLLOW = "“非”后面必须跟条件表达式或“（”。";
    /**
     * 非后面缺少表达式或左括号报错信息
     */
    private static final String MSG_NOT_MISSING = "非 后缺少条件表达式或“(“，请补充表达式或括号。";
    /**
     * 表达式不能以“或”、“且”开头报错信息
     */
    private static final String MSG_START_WITH_AND_OR = "表达式不能以“%s”开头，请调整表达式顺序。";
    /**
     * 括号内报错信息
     */
    private static final String MSG_BRACKET_EMPTY = "括号内为空，请添加表达式或删除括号。";
    /**
     * 表达式不能以“或”、“且”、“非”或左括号结尾报错信息
     */
    private static final String MSG_END_WITH_AND_OR_NOT_LEFT = "表达式不能以“%s”结尾，请调整表达式顺序。";
    /**
     * 括号配对错误报错信息
     */
    private static final String MSG_RIGHT_BRACKET_MORE = "多余的“)”，请补充对应的“(”或删除该括号。";
    /**
     * 括号配对错误报错信息
     */
    private static final String MSG_LEFT_BRACKET_MORE = "多余的“(”，请补充对应的“)”或删除该括号。";
    /**
     * 括号内全为逻辑符号报错信息
     */
    private static final String MSG_ALL_LOGIC_IN_BRACKET = "括号内不能全是逻辑符号，请添加表达式或删除括号。";
    /**
     * 括号内没有表达式报错信息
     */
    private static final String MSG_NO_EXPR_IN_BRACKET = "括号内为空，请添加表达式或删除括号。";
    /**
     * 括号内表达式之间缺少逻辑符号报错信息
     */
    private static final String MSG_BRACKET_EXPR_NO_LOGIC = "括号内表达式之间缺少逻辑符号，请补充逻辑符号。";
    /**
     * 括号内首元素不能为且/或报错信息
     */
    private static final String MSG_BRACKET_START_ANDOR = "括号内首元素不能为“%s”，请调整顺序。";
    /**
     * 括号内尾元素不能为且/或报错信息
     */
    private static final String MSG_BRACKET_END_ANDOR = "括号内尾元素不能为“%s”，请调整顺序。";
    /**
     * 表达式缺少字段名报错信息
     */
    private static final String MSG_FIRST_NULL = "首元素为空或缺少操作符，请补充操作符。";
    /**
     * 首元素为非法逻辑符号报错信息
     */
    private static final String MSG_FIRST_ILLEGAL_LOGIC = "首元素为非法逻辑符号，请调整顺序。";
    /**
     * 首元素为右括号报错信息
     */
    private static final String MSG_FIRST_EXPR_NO_KEY = "首表达式缺少字段，请补充字段名。";
    /**
     * 首元素为表达式但缺少值报错信息
     */
    private static final String MSG_FIRST_EXPR_NO_VALUE = "首表达式缺少值，请补充值。";
    /**
     * 尾元素为空或缺少操作符报错信息
     */
    private static final String MSG_LAST_NULL = "尾元素为空或缺少操作符，请补充操作符。";
    /**
     * 尾元素为且/或/非/左括号报错信息
     */
    private static final String MSG_LAST_ILLEGAL_LOGIC = "尾元素为非法逻辑符号，请调整顺序。";
    /**
     * 尾元素为表达式但缺少字段名报错信息
     */
    private static final String MSG_LAST_EXPR_NO_KEY = "尾表达式缺少字段，请补充字段名。";
    /**
     * 尾元素为表达式但缺少值报错信息
     */
    private static final String MSG_LAST_EXPR_NO_VALUE = "尾表达式缺少值，请补充值。";
    /**
     * 条件类型非法报错信息
     */
    private static final String MSG_EXPR_NO_KEY = "表达式缺少字段，请补充字段名。";
    /**
     * 表达式缺少值报错信息
     */
    private static final String MSG_EXPR_NO_VALUE = "表达式缺少值，请补充值。";
    /**
     * 表达式操作符非法报错信息
     */
    private static final String MSG_EXPR_ILLEGAL_OP = "表达式操作符非法，请使用允许的操作符。";
    /**
     * 逻辑条件操作符非法报错信息
     */
    private static final String MSG_LOGIC_ILLEGAL_OP = "逻辑条件操作符非法，请使用允许的逻辑操作符。";
    /**
     * 逻辑条件包含key或values报错信息
     */
    private static final String MSG_LOGIC_HAS_KEY_OR_VALUE = "逻辑条件不应包含key或values，请删除多余内容。";
    /**
     * 条件类型非法报错信息
     */
    private static final String MSG_ILLEGAL_TYPE = "条件类型非法，请检查类型。";
    /**
     * 右括号后不能直接跟表达式报错信息
     */
    private static final String MSG_EXPR_AFTER_RIGHT_BRACKET = "“)”后不能直接跟表达式。";
    /**
     * 括号之间缺少逻辑符号报错信息
     */
    private static final String MSG_CONSECUTIVE_BRACKETS = "括号之间缺少逻辑符号，请补充逻辑符号。";
    /**
     * 非后面不能直接连接两个表达式报错信息
     */
    private static final String MSG_NOT_BETWEEN_TWO_EXPR = "“非”不能直接连接两个条件表达式，请补充逻辑符号。";

    @Override
    public boolean isValid(List<Condition> value, ConstraintValidatorContext context) {
        if (value == null || value.isEmpty()) {
            return true;
        }
        int startIdx = 0;
        if (isVectorField(value.get(0))) {
            startIdx = 1; // 跳过第一个向量字段
        }
        if (startIdx >= value.size()) {
            return true; // 只有一个向量字段，无需校验
        }
        if (!checkBracketPairingAndContent(value, context, startIdx)) {
            return false;
        }
        return checkBlock(value, startIdx, value.size() - 1, context, true);
    }

    // 括号配对和内容校验（六种情况）
    @SuppressWarnings("checkstyle:ModifiedControlVariable")
    private boolean checkBracketPairingAndContent(List<Condition> value,
            ConstraintValidatorContext context, int startIdx) {
        int count = 0;
        java.util.Deque<Integer> leftBracketStack = new java.util.ArrayDeque<>();
        for (int i = startIdx; i < value.size(); i++) {
            Condition c = value.get(i);
            if (isLeftBracket(c)) {
                count++;
                leftBracketStack.push(i);
            }
            if (isRightBracket(c)) {
                count--;
                if (!leftBracketStack.isEmpty()) {
                    leftBracketStack.pop();
                }
            }
            if (count < 0) {
                buildMsg(context, MSG_RIGHT_BRACKET_MORE, i);
                return false;
            }
        }
        if (count > 0) {
            // 精确返回多余左括号的下标
            int errorIdx = leftBracketStack.isEmpty() ? startIdx : leftBracketStack.peek();
            buildMsg(context, MSG_LEFT_BRACKET_MORE, errorIdx);
            return false;
        }
        // 括号内容校验
        for (int i = startIdx; i < value.size(); i++) {
            if (isLeftBracket(value.get(i))) {
                if (!validateBracketContentOnce(value, i, context)) {
                    return false;
                }
                i = findMatchingRightBracket(value, i);
            }
        }
        return true;
    }

    // 校验单个括号内内容，减少主方法嵌套和变量数
    private boolean validateBracketContentOnce(List<Condition> value, int leftIdx,
            ConstraintValidatorContext context) {
        int j = findMatchingRightBracket(value, leftIdx);
        if (j == leftIdx + 1) {
            buildMsg(context, MSG_BRACKET_EMPTY + "|index=" + leftIdx + "," + j, leftIdx);
            return false;
        }
        boolean allLogic = true;
        boolean hasExpr = false;
        for (int k = leftIdx + 1; k < j; k++) {
            if (!isLogic(value.get(k))) {
                allLogic = false;
            }
            if (isExpression(value.get(k))) {
                hasExpr = true;
            }
        }
        if (allLogic) {
            buildMsg(context, MSG_ALL_LOGIC_IN_BRACKET + "|index=" + leftIdx + "," + j, leftIdx);
            return false;
        }
        if (!hasExpr) {
            buildMsg(context, MSG_NO_EXPR_IN_BRACKET, leftIdx);
            return false;
        }
        for (int k = leftIdx + 2; k < j; k++) {
            if (isExpression(value.get(k)) && isExpression(value.get(k - 1))) {
                buildMsg(context, MSG_BRACKET_EXPR_NO_LOGIC + "|index=" + (k - 1) + "," + k, leftIdx);
                return false;
            }
        }
        if (isAndOr(value.get(leftIdx + 1))) {
            buildMsg(context, String.format(MSG_BRACKET_START_ANDOR, toUserSymbol(value.get(leftIdx + 1).getOperator())), leftIdx + 1);
            return false;
        }
        if (isAndOr(value.get(j - 1))) {
            buildMsg(context, String.format(MSG_BRACKET_END_ANDOR, toUserSymbol(value.get(j - 1).getOperator())), j - 1);
            return false;
        }
        return true;
    }

    /**
     * 校验条件块的合法性
     *
     * @param conds      条件列表
     * @param start      起始索引
     * @param end        结束索引
     * @param ctx        验证上下文
     * @param isTopLevel 是否为顶层调用
     * @return boolean
     */
    @SuppressWarnings("checkstyle:ReturnCount")
    private boolean checkBlock(List<Condition> conds, int start, int end,
            ConstraintValidatorContext ctx, boolean isTopLevel) {
        if (start > end) {
            return true;
        }
        if (!validateFirstCondition(conds, start, end, ctx)) {
            return false;
        }
        if (!validateLastCondition(conds, start, end, ctx)) {
            return false;
        }
        int i = start;
        while (i <= end) {
            // 校验表达式 ( 表达式 ) 之间缺少逻辑符号
            if (
                i > start
                        &&
                isExpression(conds.get(i - 1))
                        &&
                isLeftBracket(conds.get(i))
                        &&
                i + 1 <= end
                        &&
                isExpression(conds.get(i + 1))
            ) {
                buildMsg(ctx, MSG_MISSING_LOGIC_BETWEEN_EXPR + "|index=" + (i - 1) + "," + (i + 1), i - 1);
                return false;
            }
            // 校验表达式 ) ( 表达式之间缺少逻辑符号
            if (
                i > start + 2
                        &&
                isExpression(conds.get(i - 2))
                        &&
                isRightBracket(conds.get(i - 1))
                        &&
                isLeftBracket(conds.get(i))
                        &&
                i + 1 <= end
                        &&
                isExpression(conds.get(i + 1))
            ) {
                buildMsg(ctx, MSG_MISSING_LOGIC_BETWEEN_EXPR + "|index=" + (i - 2) + "," + (i + 1), i - 2);
                return false;
            }
            // 校验右括号后不能直接跟左括号
            if (i > start && isLeftBracket(conds.get(i)) && isRightBracket(conds.get(i - 1))) {
                buildMsg(ctx, MSG_CONSECUTIVE_BRACKETS + "|index=" + (i - 1) + "," + i, i - 1);
                return false;
            }
            // 校验“非”不能直接连接两个表达式
            if (
                i > start && i < end
                && isNot(conds.get(i))
                && isExpression(conds.get(i - 1))
                && isExpression(conds.get(i + 1))
            ) {
                buildMsg(ctx, MSG_NOT_BETWEEN_TWO_EXPR, i + 1);
                return false;
            }
            if (!validateConsecutiveLogic(conds, i, start, ctx)) {
                return false;
            }
            if (!validateConsecutiveExpression(conds, i, start, ctx)) {
                return false;
            }
            if (!validateRightBracketFollowedByExpression(conds, i, start, ctx)) {
                return false;
            }
            int andOrSkip = validateAndOrRules(conds, i, start, end, ctx);
            if (andOrSkip < 0) {
                return false;
            }
            if (andOrSkip > 0) {
                i += andOrSkip;
                continue;
            }
            if (!validateNotRules(conds, i, end, ctx)) {
                return false;
            }
            int bracketSkip = validateBracketRecursively(conds, i, end, ctx);
            if (bracketSkip < 0) {
                return false;
            }
            if (bracketSkip > 0) {
                i = bracketSkip;
                continue;
            }
            i++;
        }
        if (!validateConditionContent(conds, start, end, ctx)) {
            return false;
        }
        return true;
    }

    // 校验首元素
    private boolean validateFirstCondition(List<Condition> conds, int start, int end,
            ConstraintValidatorContext ctx) {
        Condition first = conds.get(start);
        if (first == null || first.getOperator() == null) {
            buildMsg(ctx, MSG_FIRST_NULL, start);
            return false;
        }
        if (isAndOr(first)) {
            buildMsg(ctx, String.format(MSG_START_WITH_AND_OR, toUserSymbol(first.getOperator())), start);
            return false;
        }
        if (isRightBracket(first)) {
            buildMsg(ctx, "表达式不能以右括号开头", start);
            return false;
        }
        if (isLogic(first) && !isNot(first) && !isLeftBracket(first)) {
            buildMsg(ctx, MSG_FIRST_ILLEGAL_LOGIC, start);
            return false;
        }
        if (isNot(first)) {
            if (start == end) {
                buildMsg(ctx, MSG_NOT_MISSING, start);
                return false;
            }
            Condition next = conds.get(start + 1);
            if (!isExpression(next) && !isLeftBracket(next)) {
                buildMsg(ctx, MSG_NOT_FOLLOW, start);
                return false;
            }
        }
        if (isExpression(first)) {
            if (first.getKey() == null) {
                buildMsg(ctx, MSG_FIRST_EXPR_NO_KEY, start);
                return false;
            }
            if (!NO_VALUE_OPERATORS.contains(first.getOperator().toLowerCase())) {
                if (first.getValues() == null || first.getValues().isEmpty()) {
                    buildMsg(ctx, MSG_FIRST_EXPR_NO_VALUE, start);
                    return false;
                }
            }
        }
        return true;
    }

    // 校验尾元素
    private boolean validateLastCondition(List<Condition> conds, int start, int end,
            ConstraintValidatorContext ctx) {
        Condition last = conds.get(end);
        if (last == null || last.getOperator() == null) {
            buildMsg(ctx, MSG_LAST_NULL, end);
            return false;
        }
        if (isAndOr(last)) {
            buildMsg(ctx, String.format(MSG_END_WITH_AND_OR_NOT_LEFT, toUserSymbol(last.getOperator())), end);
            return false;
        }
        if (isNot(last)) {
            buildMsg(ctx, String.format(MSG_END_WITH_AND_OR_NOT_LEFT, toUserSymbol(last.getOperator())), end);
            return false;
        }
        if (isLeftBracket(last)) {
            buildMsg(ctx, String.format(MSG_END_WITH_AND_OR_NOT_LEFT, toUserSymbol(last.getOperator())), end);
            return false;
        }
        if (isLogic(last) && !isRightBracket(last)) {
            buildMsg(ctx, MSG_LAST_ILLEGAL_LOGIC, end);
            return false;
        }
        if (isExpression(last)) {
            if (last.getKey() == null) {
                buildMsg(ctx, MSG_LAST_EXPR_NO_KEY, end);
                return false;
            }
            if (!NO_VALUE_OPERATORS.contains(last.getOperator().toLowerCase())) {
                if (last.getValues() == null || last.getValues().isEmpty()) {
                    buildMsg(ctx, MSG_LAST_EXPR_NO_VALUE, end);
                    return false;
                }
            }
        }
        return true;
    }

    // 校验连续逻辑符号
    @SuppressWarnings("checkstyle:BooleanExpressionComplexity")
    private boolean validateConsecutiveLogic(List<Condition> conds, int i, int start,
            ConstraintValidatorContext ctx) {
        if (i > start && isLogic(conds.get(i)) && isLogic(conds.get(i - 1))) {
            Condition prev = conds.get(i - 1);
            Condition curr = conds.get(i);
            if (isLeftBracket(prev) && isLeftBracket(curr)
                    || isRightBracket(prev) && isRightBracket(curr)
                    || isAndOr(prev) && (isNot(curr) || isLeftBracket(curr))
                    || isNot(prev) && isLeftBracket(curr)
                    || isRightBracket(prev) && isAndOr(curr)) {
                return true;
            } else {
                buildMsg(ctx, String.format(MSG_CONSECUTIVE_LOGIC, toUserSymbol(prev.getOperator()), toUserSymbol(curr.getOperator())), i);
                return false;
            }
        }
        return true;
    }

    // 校验连续表达式
    private boolean validateConsecutiveExpression(List<Condition> conds, int i, int start,
            ConstraintValidatorContext ctx) {
        if (i > start && isExpression(conds.get(i)) && isExpression(conds.get(i - 1))) {
            buildMsg(ctx, MSG_MISSING_LOGIC_BETWEEN_EXPR + "|index=" + (i - 1) + "," + i, i);
            return false;
        }
        return true;
    }

    // 校验右括号后不能直接跟表达式
    private boolean validateRightBracketFollowedByExpression(List<Condition> conds, int i,
            int start, ConstraintValidatorContext ctx) {
        if (i > start && isExpression(conds.get(i)) && isRightBracket(conds.get(i - 1))) {
            buildMsg(ctx, MSG_EXPR_AFTER_RIGHT_BRACKET, i - 1);
            return false;
        }
        return true;
    }

    // 校验and/or前后规则，返回跳过的步数（如遇not需跳过not）
    private int validateAndOrRules(List<Condition> conds, int i, int start, int end,
            ConstraintValidatorContext ctx) {
        if (isAndOr(conds.get(i))) {
            if (i == start || i == end) {
                buildMsg(ctx, String.format(MSG_ANDOR_EDGE, toUserSymbol(conds.get(i).getOperator())), i);
                return -1;
            }
            Condition prev = conds.get(i - 1);
            Condition next = conds.get(i + 1);
            if (!(isExpression(prev) || isRightBracket(prev))) {
                buildMsg(ctx, String.format(MSG_ANDOR_BEFORE, toUserSymbol(conds.get(i).getOperator())), i);
                return -1;
            }
            if (isNot(next)) {
                if (i + 2 > end) {
                    buildMsg(ctx, MSG_NOT_MISSING, i);
                    return -1;
                }
                Condition afterNot = conds.get(i + 2);
                if (!(isExpression(afterNot) || isLeftBracket(afterNot))) {
                    buildMsg(ctx, MSG_NOT_FOLLOW, i + 1);
                    return -1;
                }
                return 1; // 跳过not
            } else if (!(isExpression(next) || isLeftBracket(next))) {
                buildMsg(ctx, String.format(MSG_ANDOR_AFTER, toUserSymbol(conds.get(i).getOperator())), i);
                return -1;
            }
        }
        return 0;
    }

    // 校验not后规则
    private boolean validateNotRules(List<Condition> conds, int i, int end,
            ConstraintValidatorContext ctx) {
        if (isNot(conds.get(i))) {
            if (i == end) {
                buildMsg(ctx, MSG_NOT_MISSING, i);
                return false;
            }
            Condition next = conds.get(i + 1);
            if (!isExpression(next) && !isLeftBracket(next)) {
                buildMsg(ctx, MSG_NOT_FOLLOW, i + 1);
                return false;
            }
        }
        return true;
    }

    // 校验括号递归，返回跳过到的下标（如递归失败返回-1）
    private int validateBracketRecursively(List<Condition> conds, int i, int end,
            ConstraintValidatorContext ctx) {
        if (isLeftBracket(conds.get(i))) {
            int j = findMatchingRightBracket(conds, i);
            if (j == i + 1) {
                buildMsg(ctx, MSG_BRACKET_EMPTY, i);
                return -1;
            }
            if (!checkBlock(conds, i + 1, j - 1, ctx, false)) {
                return -1;
            }
            return j;
        }
        return 0;
    }

    // 校验表达式内容
    private boolean validateConditionContent(List<Condition> conds, int start, int end,
            ConstraintValidatorContext ctx) {
        for (int k = start; k <= end; k++) {
            Condition c = conds.get(k);
            if (c == null) {
                buildMsg(ctx, MSG_ILLEGAL_TYPE, k);
                return false;
            }
            if (isExpression(c)) {
                if (c.getKey() == null) {
                    buildMsg(ctx, MSG_EXPR_NO_KEY, k);
                    return false;
                }
                if (!NO_VALUE_OPERATORS.contains(c.getOperator().toLowerCase())) {
                    if (c.getValues() == null || c.getValues().isEmpty()) {
                        buildMsg(ctx, MSG_EXPR_NO_VALUE, k);
                        return false;
                    }
                }
                if (!ALLOWED_OPERATORS.contains(c.getOperator().toLowerCase())) {
                    buildMsg(ctx, MSG_EXPR_ILLEGAL_OP, k);
                    return false;
                }
            } else if (isLogic(c)) {
                if (!isAllowedLogicOperator(c.getOperator())) {
                    buildMsg(ctx, MSG_LOGIC_ILLEGAL_OP, k);
                    return false;
                }
                if (c.getKey() != null || c.getValues() != null && !c.getValues().isEmpty()) {
                    buildMsg(ctx, MSG_LOGIC_HAS_KEY_OR_VALUE, k);
                    return false;
                }
            } else {
                buildMsg(ctx, MSG_ILLEGAL_TYPE, k);
                return false;
            }
        }
        return true;
    }

    // 新增：判断是否为向量字段
    private boolean isVectorField(Condition c) {
        if (c == null || c.getKey() == null || c.getKey().getType() == null) {
            return false;
        }
        return c.getKey().getType().isVectorType();
    }

    // 工具方法
    private boolean isLogic(Condition c) {
        return c != null && c.getType() == DataServiceConditionType.LOGIC;
    }

    private boolean isNot(Condition c) {
        return c != null && c.getOperator() != null && "not".equalsIgnoreCase(
                c.getOperator().trim());
    }

    private boolean isAndOr(Condition c) {
        if (c == null || c.getOperator() == null) {
            return false;
        }
        String op = c.getOperator().trim().toLowerCase();
        return "and".equals(op) || "or".equals(op);
    }

    private boolean isLeftBracket(Condition c) {
        return c != null && c.getOperator() != null && "(".equals(c.getOperator().trim());
    }

    private boolean isRightBracket(Condition c) {
        return c != null && c.getOperator() != null && ")".equals(c.getOperator().trim());
    }

    private boolean isExpression(Condition c) {
        return c != null && c.getType() != DataServiceConditionType.LOGIC;
    }

    private int findMatchingRightBracket(List<Condition> list, int leftIdx) {
        int count = 0;
        for (int i = leftIdx; i < list.size(); i++) {
            if (isLeftBracket(list.get(i))) {
                count++;
            }
            if (isRightBracket(list.get(i))) {
                count--;
            }
            if (count == 0) {
                return i;
            }
        }
        return -1;
    }

    private boolean isAllowedLogicOperator(String op) {
        if (op == null) {
            return false;
        }
        String o = op.toLowerCase();
        return "and".equals(o) || "or".equals(o) || "not".equals(o) || "(".equals(o) || ")".equals(
                o);
    }

    private static String toUserSymbol(String op) {
        if (op == null) {
            return "";
        }
        return switch (op.toLowerCase()) {
            case "and" -> "且";
            case "or" -> "或";
            case "not" -> "非";
            case "(" -> "（";
            case ")" -> "）";
            default -> op;
        };
    }

    private void buildMsg(ConstraintValidatorContext context, String msg, Integer index) {
        context.disableDefaultConstraintViolation();
        // 只拼接一次 index 字段，且为人类语义下标
        String msgWithIndex = msg + (index != null ? "|index=" + index : "");
        context.buildConstraintViolationWithTemplate(msgWithIndex).addConstraintViolation();
    }
}
