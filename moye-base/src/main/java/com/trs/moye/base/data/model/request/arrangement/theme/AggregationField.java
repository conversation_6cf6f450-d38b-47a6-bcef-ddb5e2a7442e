package com.trs.moye.base.data.model.request.arrangement.theme;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025-07-31 15:33
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class AggregationField extends SourceField {

    /**
     * 存储ID
     */
    @NotNull(message = "存储ID不能为空")
    private Integer storageId;
    /**
     * 目标字段中文名
     */
    @NotBlank(message = "目标字段中文名不能为空")
    private String targetZhName;
    /**
     * 目标字段英文名
     */
    @NotBlank(message = "目标字段英文名不能为空")
    private String targetEnName;
}
