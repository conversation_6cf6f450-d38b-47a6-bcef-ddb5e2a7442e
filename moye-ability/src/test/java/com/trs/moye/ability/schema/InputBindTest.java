package com.trs.moye.ability.schema;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertThrows;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.trs.moye.ability.entity.InputBind;
import com.trs.moye.ability.entity.InputBind.Binding;
import com.trs.moye.ability.entity.InputBind.ObjectBinding;
import com.trs.moye.ability.entity.Schema.ObjectTypeSchema;
import com.trs.moye.base.common.utils.JsonUtils;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;
import org.junit.jupiter.api.Test;

class InputBindTest {

    @Test
    void testPropertyBinding_ValidPath_ReturnsCorrectValue() {
        // 准备输入数据
        String dataJson = "{\n" +
            "  \"user\": {\n" +
            "    \"name\": \"John\",\n" +
            "    \"age\": 30\n" +
            "  }\n" +
            "}";
        ObjectNode dataNode = JsonUtils.parseObjectNode(dataJson);

        // 配置绑定规则
        InputBind inputBind = new InputBind();
        inputBind.addBinding("username", "/user/name");

        // 执行绑定和解析
        Object result = inputBind.getBinding("username").parseValue(dataNode, String.class);

        // 验证结果
        assertEquals("John", result);
    }

    @Test
    void testPropertyBinding_ArrayToArray_ConvertsCorrectly() {
        // 准备输入数据
        String dataJson = "{\"numbers\": [1, 2, 3, 4, 5]}";
        ObjectNode dataNode = JsonUtils.parseObjectNode(dataJson);

        // 配置绑定规则
        InputBind inputBind = new InputBind();
        inputBind.addBinding("numArray", "/numbers");

        // 执行绑定和解析
        Object result = inputBind.getBinding("numArray").parseValue(dataNode, Integer[].class);

        // 验证结果
        assertInstanceOf(Integer[].class, result);
        assertArrayEquals(new Integer[]{1, 2, 3, 4, 5}, (Integer[]) result);
    }

    @Test
    void testPropertyBinding_InvalidPath_ThrowsException() {
        // 准备输入数据
        String dataJson = "{\"user\": {\"name\": \"John\"}}";
        ObjectNode dataNode = JsonUtils.parseObjectNode(dataJson);

        // 配置绑定规则
        InputBind inputBind = new InputBind();
        inputBind.addBinding("age", "/user/age");

        // 验证异常
        // 验证异常
        InputBind.Binding binding = inputBind.getBinding("age");
        assertThrows(IllegalArgumentException.class, () -> binding.parseValue(dataNode, Integer.class));
    }

    @Test
    void testFixedValueBinding_StringValue_ReturnsCorrectValue() {
        // 配置绑定规则
        InputBind inputBind = new InputBind();
        inputBind.addFixedValueBinding("constant", "fixed value");

        // 执行绑定和解析 (输入JsonNode可以是null，因为不会被使用)
        Object result = inputBind.getBinding("constant").parseValue(
            JsonUtils.parseObjectNode("{}"), String.class);

        // 验证结果
        assertEquals("fixed value", result);
    }

    @Test
    void testFixedValueBinding_NumberValue_ConvertsToTargetType() {
        // 配置绑定规则
        InputBind inputBind = new InputBind();
        inputBind.addFixedValueBinding("number", 123);

        // 执行绑定和解析
        Object result = inputBind.getBinding("number").parseValue(
            JsonUtils.parseObjectNode("{}"), Integer.class);

        // 验证结果
        assertEquals(123, result);
        assertInstanceOf(Integer.class, result);
    }

    @Test
    void testFixedValueBinding_ComplexObject_ConvertsToTargetType() {
        // 准备复杂对象
        TestPerson person = new TestPerson("John", 30);

        // 配置绑定规则
        InputBind inputBind = new InputBind();
        inputBind.addFixedValueBinding("person", person);

        // 执行绑定和解析
        Object result = inputBind.getBinding("person").parseValue(
            JsonUtils.parseObjectNode("{}"), TestPerson.class);

        // 验证结果
        assertEquals("John", ((TestPerson) result).getName());
        assertEquals(30, ((TestPerson) result).getAge());
    }

    @Test
    void testArrayBinding_MultiplePaths_CombinesValuesCorrectly() {
        // 准备输入数据
        String dataJson = "{\"first\": \"Hello\",\"second\": \"World\",\"third\": \"!\"}";
        ObjectNode dataNode = JsonUtils.parseObjectNode(dataJson);

        // 配置数组绑定
        InputBind inputBind = new InputBind();
        inputBind.addArrayBinding("greeting", Arrays.asList("/first", "/second", "/third"));

        // 执行绑定和解析成字符串数组
        Object result = inputBind.getBinding("greeting").parseValue(dataNode, String[].class);

        // 验证结果
        assertInstanceOf(String[].class, result);
        assertArrayEquals(new String[]{"Hello", "World", "!"}, (String[]) result);
    }

    @Test
    void testArrayBinding_NumberValues_ConvertsToIntegerArray() {
        // 准备输入数据
        String dataJson = "{\"val1\": 10,\"val2\": 20,\"val3\": 30}";
        ObjectNode dataNode = JsonUtils.parseObjectNode(dataJson);

        // 配置数组绑定
        InputBind inputBind = new InputBind();
        inputBind.addArrayBinding("numbers", Arrays.asList("/val1", "/val2", "/val3"));

        // 执行绑定和解析成整数数组
        Object result = inputBind.getBinding("numbers").parseValue(dataNode, Integer[].class);

        // 验证结果
        assertInstanceOf(Integer[].class, result);
        assertArrayEquals(new Integer[]{10, 20, 30}, (Integer[]) result);
    }

    @Test
    void testArrayBinding_MissingPath_ThrowsException() {
        // 准备输入数据
        String dataJson = "{\"val1\": 10,\"val3\": 30}";
        ObjectNode dataNode = JsonUtils.parseObjectNode(dataJson);

        // 配置数组绑定
        InputBind inputBind = new InputBind();
        inputBind.addArrayBinding("numbers", Arrays.asList("/val1", "/val2", "/val3"));

        // 验证异常
        // 验证异常
        InputBind.Binding binding = inputBind.getBinding("numbers");
        assertThrows(IllegalArgumentException.class, () -> binding.parseValue(dataNode, Integer[].class));
    }

    @Test
    void testFromJson_CompleteConfiguration_CreatesCorrectBindings() {
        // 准备绑定配置的JSON
        String bindingJson = "{\n" +
            "  \"name\": {\n" +
            "    \"jsonPath\": \"/user/name\",\n" +
            "    \"order\": 1\n" +
            "  },\n" +
            "  \"age\": {\n" +
            "    \"jsonPath\": \"/user/age\",\n" +
            "    \"order\": 2\n" +
            "  },\n" +
            "  \"fixed\": {\n" +
            "    \"fixedValue\": \"constant\",\n" +
            "    \"order\": 0\n" +
            "  },\n" +
            "  \"combined\": {\n" +
            "    \"jsonPaths\": [\"/val1\", \"/val2\"],\n" +
            "    \"order\": 3\n" +
            "  }\n" +
            "}";

        JsonNode bindingConfig = JsonUtils.parseJsonNode(bindingJson);

        // 从JSON创建InputBind
        InputBind inputBind = InputBind.fromJson(bindingConfig);

        // 验证绑定已正确创建
        assertEquals(4, inputBind.getBindings().size());
        assertInstanceOf(InputBind.PropertyBinding.class, inputBind.getBinding("name"));
        assertInstanceOf(InputBind.PropertyBinding.class, inputBind.getBinding("age"));
        assertInstanceOf(InputBind.FixedValueBinding.class, inputBind.getBinding("fixed"));
        assertInstanceOf(InputBind.ArrayBinding.class, inputBind.getBinding("combined"));
    }

    @Test
    void testGetParamsMap_ValidInput_ReturnsCorrectMapping() {
        // 准备输入数据
        String dataJson = "{\n" +
            "  \"user\": {\n" +
            "    \"name\": \"Jane\",\n" +
            "    \"age\": 25\n" +
            "  },\n" +
            "  \"scores\": [80, 90, 95]\n" +
            "}";
        ObjectNode dataNode = JsonUtils.parseObjectNode(dataJson);

        // 配置绑定规则
        InputBind inputBind = new InputBind();
        inputBind.addBinding("name", "/user/name");
        inputBind.addBinding("age", "/user/age");
        inputBind.addBinding("scoreList", "/scores");

        // 获取参数映射
        Map<String, Object> paramsMap = inputBind.getParamsMap(dataNode, new ObjectTypeSchema());

        // 验证映射结果
        assertEquals(3, paramsMap.size());
        assertEquals("Jane", paramsMap.get("name (来源字段:/user/name)"));
        assertEquals(25, paramsMap.get("age (来源字段:/user/age)"));
        assertInstanceOf(List.class, paramsMap.get("scoreList (来源字段:/scores)"));
    }

    @Test
    void testPropertyBinding_JsonArrayToString_ConvertsProperly() {
        // 准备输入数据：包含一个字符串数组
        String dataJson = "{\"tags\": [\"java\", \"spring\", \"junit\"]}";
        ObjectNode dataNode = JsonUtils.parseObjectNode(dataJson);

        // 配置绑定规则：将数组路径绑定到一个String类型参数
        InputBind inputBind = new InputBind();
        inputBind.addBinding("tagsString", "/tags");

        // 执行绑定和解析，将JSON数组解析为String类型
        Object result = inputBind.getBinding("tagsString").parseValue(dataNode, String.class);

        // 验证结果：应该是包含数组字符串表示的字符串
        assertInstanceOf(String.class, result);
        // 检查字符串内容是否符合预期（这里是JSON数组的字符串表示）
        assertEquals("[\"java\",\"spring\",\"junit\"]", result);
    }

    // 用于测试复杂对象转换的辅助类
    @Setter
    @Getter
    static class TestPerson {

        private String name;
        private int age;

        public TestPerson(String name, int age) {
            this.name = name;
            this.age = age;
        }

    }

    @Test
    void testArrayBinding_ComplexObject_ConvertsToListOfObjects() {
        // 准备输入数据：包含一个复杂对象数组
        String dataJson = "{\"name\": \"Alice\", \"age\": 30}";
        JsonNode dataNode = JsonUtils.toJsonNode(dataJson);

        String bindingJson = "{\n"
            + "    \"info\": {\n"
            + "        \"order\": 0,\n"
            + "        \"items\": [\n"
            + "            {\n"
            + "                \"type\": {\n"
            + "                    \"fixedValue\": \"name\"\n"
            + "                },\n"
            + "                \"value\": {\n"
            + "                    \"jsonPath\": \"/name\"\n"
            + "                }\n"
            + "            },\n"
            + "            {\n"
            + "                \"type\": {\n"
            + "                    \"fixedValue\": \"age\"\n"
            + "                },\n"
            + "                \"value\": {\n"
            + "                    \"jsonPath\": \"/age\"\n"
            + "                }\n"
            + "            }\n"
            + "        ]\n"
            + "    }\n"
            + "}";
        JsonNode bindingNode = JsonUtils.toJsonNode(bindingJson);
        // 配置数组绑定
        InputBind inputBind = InputBind.fromJson(bindingNode);

        // 执行绑定和解析成复杂对象列表
        Object result = inputBind.getBinding("info").parseValue(dataNode, TestPerson[].class);

        // 验证结果
        JsonNode resultNode = JsonUtils.toJsonNode(JsonUtils.toJsonString(result));
        ArrayNode arrayNode = (ArrayNode) resultNode;
        assertEquals(2, arrayNode.size());
        assertEquals("name", arrayNode.get(0).get("type").asText());
        assertEquals("Alice", arrayNode.get(0).get("value").asText());
        assertEquals("age", arrayNode.get(1).get("type").asText());
        assertEquals(30, arrayNode.get(1).get("value").asInt());
    }

    @Test
    void testArrayBinding_fixValuedArray2ObjectArrayBinding() {
        // 准备输入数据
        String dataJson = "{\"name\": \"Alice\", \"age\": 30}";
        JsonNode dataNode = JsonUtils.toJsonNode(dataJson);

        String bindingJson = "{\n"
            + "    \"msg\": {\n"
            + "        \"order\": 0,\n"
            + "        \"fixedValue\": [\n"
            + "            {\n"
            + "                \"content\": \"think\",\n"
            + "                \"role\": \"system\"\n"
            + "            }\n"
            + "        ]\n"
            + "    }\n"
            + "}";
        JsonNode bindingNode = JsonUtils.toJsonNode(bindingJson);
        // 配置数组绑定
        InputBind inputBind = InputBind.fromJson(bindingNode);

        // 执行绑定和解析，将JSON数组解析为List类型
        Object result = inputBind.getBinding("msg").parseValue(dataNode, Object.class);

        String newBindingJson = JsonUtils.toJsonString(inputBind);
        // 验证结果
        assertInstanceOf(List.class, result);
    }

    /**
     * 测试对象绑定
     */
    @Test
    void testArrayBinding_ObjectBinding() {
        // 准备输入数据
        String dataJson = "{\"name\": \"Alice\", \"age\": 30}";
        JsonNode dataNode = JsonUtils.toJsonNode(dataJson);

        String bindingJson = "{\n"
            + "    \"messages\": {\n"
            + "        \"order\": 0,\n"
            + "        \"properties\": {\n"
            + "            \"content\": {\n"
            + "                \"fixedValue\": \"think\"\n"
            + "            },\n"
            + "            \"role\": {\n"
            + "                \"jsonPath\": \"/name\"\n"
            + "            }\n"
            + "        }\n"
            + "    },\n"
            + "    \"temperature\": {\n"
            + "        \"order\": 0,\n"
            + "        \"fixedValue\": \"0.3\"\n"
            + "    }\n"
            + "}";
        JsonNode bindingNode = JsonUtils.toJsonNode(bindingJson);
        // 配置数组绑定
        InputBind inputBind = InputBind.fromJson(bindingNode);

        // 执行绑定和解析，将JSON数组解析为List类型
        Object messages = inputBind.getBinding("messages").parseValue(dataNode, Object.class);

        // 验证结果
        assertInstanceOf(HashMap.class, messages);
        HashMap<String, Object> map = (HashMap<String, Object>) messages;
        assertEquals("think", map.get("content"));
        assertEquals("Alice", map.get("role"));
        Object temperature = inputBind.getBinding("temperature").parseValue(dataNode, Object.class);
        assertEquals("0.3", temperature);
    }

    /**
     * 测试旧数据格式的数组绑定到对象绑定
     */
    @Test
    void testArrayBinding_OldData2ObjectBinding() {
        // 准备输入数据
        String dataJson = "{\"name\": \"Alice\", \"role\": \"user\"}";
        JsonNode dataNode = JsonUtils.toJsonNode(dataJson);

        String bindingJson = "{\n"
            + "    \"messages.content\": {\n"
            + "        \"fixedValue\": \"think\"\n"
            + "    },\n"
            + "    \"messages.role\": {\n"
            + "        \"jsonPath\": \"/role\"\n"
            + "    },\n"
            + "    \"messages.test.a\": {\n"
            + "        \"fixedValue\": \"a\"\n"
            + "    },\n"
            + "    \"messages.test.b\": {\n"
            + "        \"fixedValue\": \"b\"\n"
            + "    },\n"
            + "    \"temperature\": {\n"
            + "        \"order\": 0,\n"
            + "        \"fixedValue\": \"0.3\"\n"
            + "    }\n"
            + "}";
        JsonNode bindingNode = JsonUtils.toJsonNode(bindingJson);
        // 配置数组绑定
        InputBind inputBind = InputBind.fromJson(bindingNode);

        // 执行绑定和解析
        Binding binding = inputBind.getBinding("messages");
        assertInstanceOf(ObjectBinding.class, binding);
        Object messages = binding.parseValue(dataNode, Object.class);

        // 验证结果
        assertInstanceOf(HashMap.class, messages);
        HashMap<String, Object> map = (HashMap<String, Object>) messages;
        assertEquals("think", map.get("content"));
        assertEquals("user", map.get("role"));
        HashMap<String, Object> test = (HashMap<String, Object>) ((HashMap<?, ?>) messages).get("test");
        assertEquals("a", test.get("a"));
        assertEquals("b", test.get("b"));
        Object temperature = inputBind.getBinding("temperature").parseValue(dataNode, Object.class);
        assertEquals("0.3", temperature);
    }
}