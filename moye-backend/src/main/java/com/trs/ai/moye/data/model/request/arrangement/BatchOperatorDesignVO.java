package com.trs.ai.moye.data.model.request.arrangement;

import com.trs.ai.moye.data.model.dto.arrangement.BatchOperatorDetailVO;
import com.trs.moye.base.data.model.entity.Canvas;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 批处理算子编排
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BatchOperatorDesignVO {

    /**
     * 算子编排信息
     */
    private List<BatchOperatorDetailVO> operators;
    /**
     * 画布信息
     */
    private Canvas canvas;

}
