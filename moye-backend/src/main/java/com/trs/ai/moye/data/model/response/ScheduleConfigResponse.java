package com.trs.ai.moye.data.model.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.trs.moye.base.common.typehandler.PolymorphismTypeHandler;
import com.trs.moye.base.data.model.entity.DataModelScheduleConfig;
import com.trs.moye.base.data.schedule.ScheduleInfo;
import java.util.Objects;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-11 21:30
 */
@Data
@NoArgsConstructor
public class ScheduleConfigResponse {

    /**
     * 调度信息
     */
    @TableField(typeHandler = PolymorphismTypeHandler.class)
    private ScheduleInfo scheduleInfo;

    public ScheduleConfigResponse(DataModelScheduleConfig scheduleConfig) {
        this.scheduleInfo = Objects.isNull(scheduleConfig) ? null : scheduleConfig.getScheduleInfo();
    }
}
