package com.trs.moye.base.data.model.request.arrangement.theme;

import com.trs.moye.base.common.annotaion.validation.NotRepeat;
import com.trs.moye.base.data.model.entity.Canvas;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025-07-31 14:58
 */
@Data
public class ArrangeSourceTable {

    /**
     * 数据模型ID
     */
    @NotNull(message = "数据模型ID不能为空")
    private Integer dataModelId;

    /**
     * 数据模型名称
     */
    @NotBlank(message = "数据模型名称不能为空")
    private String dataModelName;

    /**
     * 存储ID
     */
    @NotNull(message = "存储ID不能为空")
    private Integer storageId;

    /**
     * 画布信息
     */
    private Canvas canvas;

    /**
     * 字段列表
     */
    @NotEmpty(message = "字段列表不能为空")
    @Valid
    @NotRepeat(message = "来源表字段英文名不能重复", fields = {"enName"})
    private List<SourceField> fields;
}
