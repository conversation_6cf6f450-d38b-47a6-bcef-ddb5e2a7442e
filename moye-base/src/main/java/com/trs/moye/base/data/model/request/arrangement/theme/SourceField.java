package com.trs.moye.base.data.model.request.arrangement.theme;

import com.trs.moye.base.common.enums.FieldType;
import com.trs.moye.base.data.model.entity.DataModelField;
import javax.validation.constraints.NotBlank;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025-07-31 15:33
 */
@Data
@NoArgsConstructor
public class SourceField {

    /**
     * 源字段中文名
     */
    @NotBlank
    private String zhName;
    /**
     * 源字段英文名
     */
    @NotBlank
    private String enName;
    /**
     * 字段类型
     */
    @NotBlank
    private FieldType type;
    /**
     * 字段类型中文名
     */
    @NotBlank
    private String typeName;

    /**
     * 允许多值。1：是否允许多值。1：是；0：否；默认否
     */
    protected boolean isMultiValue;

    /**
     * 是否被选择
     */
    private boolean isChecked;

    /**
     * 是否建表，该字段是否已经被用于建表，该字段冗余保存到数据库中，返回给前端时应该查询数据库实时更新该字段值
     */
    private boolean isCreateTable;

    /**
     * 是否建表，该字段是否已经被用于建表，该字段冗余保存到数据库中，返回给前端时应该查询数据库实时更新该字段值
     */
    private boolean isExist;

    public SourceField(DataModelField field){
        this.zhName = field.getZhName();
        this.enName = field.getEnName();
        this.type = field.getType();
        this.typeName = field.getTypeName();
        this.isMultiValue = field.isMultiValue();
    }
}
